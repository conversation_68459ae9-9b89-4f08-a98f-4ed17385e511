server {
    listen 80;
    #listen 443 ssl;
    server_name upgrade-admin.zenmen.com;
    error_log /data/logs/upgrade-admin.zenmen.com-error.log;
    access_log /data/logs/upgrade-admin.zenmen.com-access.log main;

    #if ( $scheme = http ){
    #    return 301 https://$server_name$request_uri;
    #}

    #ssl_certificate /data/ssl/zenmen.com/cdn.zenmen.com.crt;
    #ssl_certificate_key /data/ssl/zenmen.com/cdn.zenmen.com.key;
    #ssl_session_cache    shared:SSL:1m;
    #ssl_session_timeout  5m;
    error_page 502 /502.htm;
    client_header_buffer_size 21m;
    client_max_body_size 21m;

    root /data/app/zchatim-upgradeadmin-prod/;
    index index.html index.htm;

    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto https;


    add_header Access-Control-Allow-Origin *;
    add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
    add_header Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';
    if ($request_method = 'OPTIONS') {
        return 204;
    }

    location / {
        add_header Cache-Control no-cache;
        root /data/app/zchatim-upgradeadmin-prod/;
        try_files $uri $uri/ /index.html;
        expires 1h;
    }

    location ~ .*\.(ico|gif|jpg|jpeg|png|css|js|svg|woff|woff2|font-woff)(.*) {
        expires 1h;
    }

}
