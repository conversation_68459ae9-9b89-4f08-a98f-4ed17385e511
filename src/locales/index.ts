import { createI18n } from 'vue-i18n'
import zhC<PERSON> from './zh-C<PERSON>'
import enUS from './en-US'

// 支持的语言
export const SUPPORT_LOCALES = ['zh-CN', 'en-US'] as const
export type SupportLocale = typeof SUPPORT_LOCALES[number]

// 语言名称映射
export const LOCALE_NAMES: Record<SupportLocale, string> = {
  'zh-CN': '简体中文',
  'en-US': 'English'
}

// 获取浏览器语言
export function getBrowserLocale(): SupportLocale {
  const browserLang = navigator.language || navigator.languages[0]
  
  // 直接匹配
  if (SUPPORT_LOCALES.includes(browserLang as SupportLocale)) {
    return browserLang as SupportLocale
  }
  
  // 匹配语言前缀
  const langPrefix = browserLang.split('-')[0]
  for (const locale of SUPPORT_LOCALES) {
    if (locale.startsWith(langPrefix)) {
      return locale
    }
  }
  
  // 默认返回中文
  return 'zh-CN'
}

// 获取存储的语言设置
export function getStoredLocale(): SupportLocale {
  const stored = localStorage.getItem('locale')
  if (stored && SUPPORT_LOCALES.includes(stored as SupportLocale)) {
    return stored as SupportLocale
  }
  return getBrowserLocale()
}

// 保存语言设置
export function setStoredLocale(locale: SupportLocale): void {
  localStorage.setItem('locale', locale)
}

// 创建 i18n 实例
export const i18n = createI18n({
  legacy: false, // 使用 Composition API
  locale: getStoredLocale(),
  fallbackLocale: 'zh-CN',
  messages: {
    'zh-CN': zhCN,
    'en-US': enUS
  },
  globalInjection: true, // 全局注入 $t
  silentTranslationWarn: import.meta.env.PROD, // 生产环境下静默警告
  silentFallbackWarn: import.meta.env.PROD
})

// 切换语言
export function switchLocale(locale: SupportLocale): void {
  if (!SUPPORT_LOCALES.includes(locale)) {
    console.warn(`Unsupported locale: ${locale}`)
    return
  }
  
  i18n.global.locale.value = locale
  setStoredLocale(locale)
  
  // 更新 HTML lang 属性
  document.documentElement.setAttribute('lang', locale)
  
  // 更新页面标题（如果需要）
  document.title = i18n.global.t('common.appName', 'Zen Upgrade Admin') as string
}

// 获取当前语言
export function getCurrentLocale(): SupportLocale {
  return i18n.global.locale.value as SupportLocale
}

// 检查是否为 RTL 语言
export function isRTL(locale?: SupportLocale): boolean {
  const rtlLocales = ['ar', 'he', 'fa']
  const currentLocale = locale || getCurrentLocale()
  return rtlLocales.some(rtl => currentLocale.startsWith(rtl))
}

// 格式化消息（支持插值）
export function formatMessage(key: string, values?: Record<string, any>): string {
  return i18n.global.t(key, values) as string
}

// 带默认值的翻译
export function translate(key: string, defaultValue?: string, values?: Record<string, any>): string {
  const result = i18n.global.t(key, values)
  return result === key ? (defaultValue || key) : result as string
}

// 复数形式翻译
export function translatePlural(key: string, count: number, values?: Record<string, any>): string {
  return i18n.global.tc(key, count, values) as string
}

// 日期时间格式化
export function formatDateTime(date: Date | string | number, format?: string): string {
  const d = new Date(date)
  const locale = getCurrentLocale()
  
  if (format) {
    // 自定义格式
    return new Intl.DateTimeFormat(locale, {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    }).format(d)
  }
  
  // 相对时间
  const now = new Date()
  const diff = now.getTime() - d.getTime()
  const seconds = Math.floor(diff / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)
  const days = Math.floor(hours / 24)
  const months = Math.floor(days / 30)
  const years = Math.floor(months / 12)
  
  if (seconds < 60) {
    return translate('datetime.now')
  } else if (minutes < 60) {
    return translate('datetime.minutesAgo', undefined, { minutes })
  } else if (hours < 24) {
    return translate('datetime.hoursAgo', undefined, { hours })
  } else if (days < 30) {
    return translate('datetime.daysAgo', undefined, { days })
  } else if (months < 12) {
    return translate('datetime.monthsAgo', undefined, { months })
  } else {
    return translate('datetime.yearsAgo', undefined, { years })
  }
}

// 数字格式化
export function formatNumber(num: number, options?: Intl.NumberFormatOptions): string {
  const locale = getCurrentLocale()
  return new Intl.NumberFormat(locale, options).format(num)
}

// 文件大小格式化
export function formatFileSize(bytes: number): string {
  const locale = getCurrentLocale()
  const units = locale === 'zh-CN' ? ['字节', 'KB', 'MB', 'GB', 'TB'] : ['Bytes', 'KB', 'MB', 'GB', 'TB']
  
  if (bytes === 0) return `0 ${units[0]}`
  
  const k = 1024
  const dm = 2
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(dm))} ${units[i]}`
}

export default i18n

