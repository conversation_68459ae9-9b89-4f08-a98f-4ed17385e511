export default {
  // 通用
  common: {
    confirm: '确认',
    cancel: '取消',
    ok: '确定',
    save: '保存',
    edit: '编辑',
    delete: '删除',
    create: '创建',
    add: '添加',
    search: '搜索',
    loading: '加载中...',
    noData: '暂无数据',
    operation: '操作',
    success: '成功',
    error: '错误',
    warning: '警告',
    info: '信息',
    submit: '提交',
    reset: '重置',
    close: '关闭',
    back: '返回',
    next: '下一步',
    previous: '上一步',
    refresh: '刷新',
    export: '导出',
    import: '导入'
  },

  // 导航菜单
  menu: {
    home: '首页',
    apiKeyManage: 'API Key 管理',
    appManage: '应用管理',
    publishManage: '发布管理',
    packageManage: '包管理'
  },

  // 主题相关
  theme: {
    light: '浅色模式',
    dark: '深色模式',
    system: '跟随系统',
    toggle: '切换主题'
  },

  // 用户相关
  user: {
    login: '登录',
    logout: '退出登录',
    profile: '个人资料',
    settings: '设置'
  },

  // 应用管理
  app: {
    title: '应用管理',
    list: '应用列表',
    create: '创建应用',
    createApp: '创建新应用',
    edit: '编辑应用',
    delete: '删除应用',
    deleteConfirm: '确定要删除这个应用吗？',
    deleteSuccess: '应用删除成功',
    deleteError: '应用删除失败',
    createSuccess: '应用创建成功',
    createError: '应用创建失败',
    updateSuccess: '应用更新成功',
    loadError: '加载应用列表失败',
    fields: {
      id: '应用ID',
      name: '应用名称',
      packageName: '包名',
      platform: '平台',
      createdAt: '创建时间',
      updatedAt: '更新时间'
    },
    form: {
      appNamePlaceholder: '请输入应用名称',
      packageNamePlaceholder: '请输入包名',
      platformPlaceholder: '请输入平台，如：android 或 ios',
      appNameHelp: '请输入应用的显示名称',
      packageNameHelp: '请输入应用的包名，如：com.example.app',
      platformHelp: '请输入应用平台：android 或 ios'
    }
  },

  // API Key 管理
  apiKey: {
    title: 'API Key 管理',
    list: 'API Key 列表',
    create: '创建 API Key',
    delete: '删除 API Key',
    deleteConfirm: '确定要删除这个 API Key 吗？',
    deleteSuccess: 'API Key 删除成功',
    createSuccess: 'API Key 创建成功',
    fields: {
      name: '名称',
      key: '密钥',
      createdAt: '创建时间',
      lastUsed: '最后使用'
    },
    form: {
      namePlaceholder: '请输入 API Key 名称',
      descriptionPlaceholder: '请输入描述（可选）'
    }
  },

  // 发布管理
  publish: {
    title: '发布管理',
    list: '发布列表',
    create: '创建发布',
    edit: '编辑发布',
    delete: '删除发布',
    publish: '发布',
    activate: '激活',
    deactivate: '停用',
    fields: {
      packageName: '包名',
      platform: '平台',
      channel: '渠道',
      updateType: '更新类型',
      versionName: '版本名称',
      versionCode: '版本号',
      features: '更新特性',
      downloadUrl: '下载地址',
      officialSite: '官方网站',
      locale: '语言',
      active: '状态',
      minBinaryVersionCode: '最小兼容版本',
      ext: '扩展信息'
    }
  },

  // 包管理
  package: {
    title: '包管理',
    list: '包列表',
    upload: '上传包',
    download: '下载',
    fields: {
      packageName: '包名',
      platform: '平台',
      versionName: '版本名称',
      versionCode: '版本号',
      fileHash: '文件哈希',
      fileSize: '文件大小',
      uploadTime: '上传时间'
    }
  },

  // 错误消息
  error: {
    networkError: '网络连接错误',
    serverError: '服务器错误',
    unauthorized: '未授权访问',
    forbidden: '权限不足',
    notFound: '请求的资源不存在',
    timeout: '请求超时',
    validationError: '验证错误',
    unknownError: '未知错误',
    offline: '网络连接已断开',
    online: '网络连接已恢复',
    loginExpired: '登录已过期，请重新登录'
  },

  // 成功消息
  success: {
    operationSuccess: '操作成功',
    saveSuccess: '保存成功',
    deleteSuccess: '删除成功',
    createSuccess: '创建成功',
    updateSuccess: '更新成功'
  },

  // 验证消息
  validation: {
    required: '{field}是必填项',
    minLength: '{field}最少需要{min}个字符',
    maxLength: '{field}最多只能有{max}个字符',
    invalidFormat: '{field}格式不正确',
    invalidEmail: '请输入正确的邮箱格式',
    invalidPhone: '请输入正确的手机号码',
    invalidUrl: '请输入正确的URL格式',
    invalidPackageName: '包名格式不正确，例如：com.example.app',
    invalidVersion: '版本号格式不正确，例如：1.0.0',
    alphanumericOnly: '只能包含字母和数字',
    platformRequired: '平台只能是 android 或 ios'
  },

  // 确认对话框
  confirm: {
    deleteTitle: '删除确认',
    deleteContent: '此操作将永久删除该项，是否继续？',
    unsavedChanges: '您有未保存的更改，确定要离开吗？',
    logout: '确定要退出登录吗？'
  },

  // 分页
  pagination: {
    total: '共 {total} 条',
    pageSize: '每页显示',
    current: '第 {current} 页',
    prev: '上一页',
    next: '下一页',
    goto: '跳转到',
    page: '页'
  },

  // 日期时间
  datetime: {
    now: '刚刚',
    minutesAgo: '{minutes} 分钟前',
    hoursAgo: '{hours} 小时前',
    daysAgo: '{days} 天前',
    monthsAgo: '{months} 个月前',
    yearsAgo: '{years} 年前'
  }
}
