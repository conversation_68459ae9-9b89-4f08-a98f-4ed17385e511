export default {
  // Common
  common: {
    confirm: 'Confirm',
    cancel: 'Cancel',
    ok: 'OK',
    save: 'Save',
    edit: 'Edit',
    delete: 'Delete',
    create: 'Create',
    add: 'Add',
    search: 'Search',
    loading: 'Loading...',
    noData: 'No Data',
    operation: 'Operation',
    success: 'Success',
    error: 'Error',
    warning: 'Warning',
    info: 'Info',
    submit: 'Submit',
    reset: 'Reset',
    close: 'Close',
    back: 'Back',
    next: 'Next',
    previous: 'Previous',
    refresh: 'Refresh',
    export: 'Export',
    import: 'Import'
  },

  // Navigation menu
  menu: {
    home: 'Home',
    apiKeyManage: 'API Key Management',
    appManage: 'App Management',
    publishManage: 'Publish Management',
    packageManage: 'Package Management'
  },

  // Theme
  theme: {
    light: 'Light Mode',
    dark: 'Dark Mode',
    system: 'Follow System',
    toggle: 'Toggle Theme'
  },

  // User
  user: {
    login: 'Login',
    logout: 'Logout',
    profile: 'Profile',
    settings: 'Settings'
  },

  // App Management
  app: {
    title: 'App Management',
    list: 'App List',
    create: 'Create App',
    createApp: 'Create New App',
    edit: 'Edit App',
    delete: 'Delete App',
    deleteConfirm: 'Are you sure you want to delete this app?',
    deleteSuccess: 'App deleted successfully',
    createSuccess: 'App created successfully',
    updateSuccess: 'App updated successfully',
    fields: {
      id: 'App ID',
      name: 'App Name',
      packageName: 'Package Name',
      platform: 'Platform',
      createdAt: 'Created At',
      updatedAt: 'Updated At'
    },
    form: {
      appNamePlaceholder: 'Please enter app name',
      packageNamePlaceholder: 'Please enter package name',
      platformPlaceholder: 'Please enter platform, e.g.: android or ios',
      appNameHelp: 'Please enter the display name of the app',
      packageNameHelp: 'Please enter the package name, e.g.: com.example.app',
      platformHelp: 'Please enter app platform: android or ios'
    }
  },

  // API Key Management
  apiKey: {
    title: 'API Key Management',
    list: 'API Key List',
    create: 'Create API Key',
    delete: 'Delete API Key',
    deleteConfirm: 'Are you sure you want to delete this API Key?',
    deleteSuccess: 'API Key deleted successfully',
    createSuccess: 'API Key created successfully',
    fields: {
      name: 'Name',
      key: 'Key',
      createdAt: 'Created At',
      lastUsed: 'Last Used'
    },
    form: {
      namePlaceholder: 'Please enter API Key name',
      descriptionPlaceholder: 'Please enter description (optional)'
    }
  },

  // Publish Management
  publish: {
    title: 'Publish Management',
    list: 'Publish List',
    create: 'Create Publish',
    edit: 'Edit Publish',
    delete: 'Delete Publish',
    publish: 'Publish',
    activate: 'Activate',
    deactivate: 'Deactivate',
    fields: {
      packageName: 'Package Name',
      platform: 'Platform',
      channel: 'Channel',
      updateType: 'Update Type',
      versionName: 'Version Name',
      versionCode: 'Version Code',
      features: 'Update Features',
      downloadUrl: 'Download URL',
      officialSite: 'Official Site',
      locale: 'Locale',
      active: 'Status',
      minBinaryVersionCode: 'Min Binary Version Code',
      ext: 'Extension'
    }
  },

  // Package Management
  package: {
    title: 'Package Management',
    list: 'Package List',
    upload: 'Upload Package',
    download: 'Download',
    fields: {
      packageName: 'Package Name',
      platform: 'Platform',
      versionName: 'Version Name',
      versionCode: 'Version Code',
      fileHash: 'File Hash',
      fileSize: 'File Size',
      uploadTime: 'Upload Time'
    }
  },

  // Error messages
  error: {
    networkError: 'Network connection error',
    serverError: 'Server error',
    unauthorized: 'Unauthorized access',
    forbidden: 'Insufficient permissions',
    notFound: 'Requested resource not found',
    timeout: 'Request timeout',
    validationError: 'Validation error',
    unknownError: 'Unknown error',
    offline: 'Network connection lost',
    online: 'Network connection restored',
    loginExpired: 'Login expired, please login again'
  },

  // Success messages
  success: {
    operationSuccess: 'Operation successful',
    saveSuccess: 'Saved successfully',
    deleteSuccess: 'Deleted successfully',
    createSuccess: 'Created successfully',
    updateSuccess: 'Updated successfully'
  },

  // Validation messages
  validation: {
    required: '{field} is required',
    minLength: '{field} requires at least {min} characters',
    maxLength: '{field} can have at most {max} characters',
    invalidFormat: '{field} format is incorrect',
    invalidEmail: 'Please enter a valid email format',
    invalidPhone: 'Please enter a valid phone number',
    invalidUrl: 'Please enter a valid URL format',
    invalidPackageName: 'Invalid package name format, e.g.: com.example.app',
    invalidVersion: 'Invalid version format, e.g.: 1.0.0',
    alphanumericOnly: 'Only letters and numbers are allowed',
    platformRequired: 'Platform can only be android or ios'
  },

  // Confirm dialogs
  confirm: {
    deleteTitle: 'Delete Confirmation',
    deleteContent: 'This operation will permanently delete this item. Continue?',
    unsavedChanges: 'You have unsaved changes. Are you sure you want to leave?',
    logout: 'Are you sure you want to logout?'
  },

  // Pagination
  pagination: {
    total: 'Total {total} items',
    pageSize: 'Items per page',
    current: 'Page {current}',
    prev: 'Previous',
    next: 'Next',
    goto: 'Go to',
    page: 'Page'
  },

  // DateTime
  datetime: {
    now: 'Just now',
    minutesAgo: '{minutes} minutes ago',
    hoursAgo: '{hours} hours ago',
    daysAgo: '{days} days ago',
    monthsAgo: '{months} months ago',
    yearsAgo: '{years} years ago'
  }
}

