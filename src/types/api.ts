// API 响应的基础类型
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  success: boolean
}

// 用户相关类型
export interface User {
  id: string
  username: string
  display_name: string
  email?: string
  avatar?: string
  created_at: string
  updated_at: string
}

export interface LoginRequest {
  code: string
}

export interface LoginResponse {
  token: string
  user: User
  expires_in: number
}

// 应用相关类型
export interface App {
  _id: string
  app_name: string
  package_name: string
  platform: 'android' | 'ios'
  created_at: string
  updated_at: string
}

export interface CreateAppRequest {
  app_name: string
  package_name: string
  platform: 'android' | 'ios'
}

// 包相关类型
export interface PackageFile {
  id: string
  package_name: string
  platform: string
  version_name: string
  version_code: string
  file_hash: string
  file_size: number
  upload_time: string
  file_path: string
}

export interface UploadPackagesRequest {
  package_name: string
  platform: string
}

// 获取发布列表的请求参数
export interface GetPublishListRequest {
  package_name: string
  platform: string
}

// 发布相关类型
export interface PublishInfo {
  id: string
  package_name: string
  platform: string
  channel: string
  update_type: number
  version_name: string
  version_code: string
  upgrade_features: string
  package_downloadUrl: string
  official_site: string
  locale: string
  active: number
  min_binary_version_code: string
  ext: string
  created_at: string
  updated_at: string
}

export interface CreatePublishRequest {
  package_name: string
  platform: string
  channel: string
  update_type: number
  version_name: string
  version_code: string
  upgrade_features: string
  package_downloadUrl: string
  official_site: string
  locale: string
  active: number
  min_binary_version_code: string
  ext: string
}

export interface UpdatePublishRequest extends CreatePublishRequest {
  publish_id: string
}

export interface PublishPackageRequest {
  package_id: string
  upgrade_features: string
  update_type: number
  package_downloadUrl: string
  official_site: string
  locale: string
}

export interface ActivePublishRequest {
  publih_id: string
  active: string
}

export interface GetDownloadUrlRequest {
  file_hash: string
}

export interface DeleteAppRequest {
  app_id: string
}

export interface DeletePublishRequest {
  publish_id: string
}

// 分页相关类型
export interface PaginationParams {
  page?: number
  pageSize?: number
  total?: number
}

export interface PaginatedResponse<T> {
  data: T[]
  pagination: {
    current: number
    pageSize: number
    total: number
    totalPages: number
  }
}

// 错误类型
export interface ApiError {
  code: number
  message: string
  details?: any
  timestamp: string
}

// 通用请求参数类型
export type RequestParams = Record<string, any>
export type RequestBody = Record<string, any>

