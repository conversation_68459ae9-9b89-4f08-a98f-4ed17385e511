import './assets/main.less'

import { createApp } from 'vue'
import { createPinia } from 'pinia'

import App from './App.vue'
import router from './router'
import ArcoVue from '@arco-design/web-vue'
import '@arco-design/web-vue/dist/arco.css'
import { userApi } from '@/networking/api'
import res_auth from '@/networking/interceptors/res_auth'
import req_auth from '@/networking/interceptors/req_auth'
import { rClient } from './networking/api/basicClient'
import { useThemeStore } from '@/stores/themeStore'
import { i18n } from '@/locales'
import { errorHandler } from '@/utils/errorHandler'
import { cleanupCorruptedStorage } from '@/utils/localStorage'

rClient.setBaseURL(import.meta.env.VITE_COMMON_ENDPOINT)
rClient.setRequestInterceptors([req_auth])
rClient.setResponseInterceptors([res_auth])

const app = createApp(App)
const pinia = createPinia()

app.use(pinia)
app.use(router)
app.use(ArcoVue)
app.use(i18n)

// 初始化错误处理
window.addEventListener('error', (event) => {
  errorHandler.handleApiError({
    message: event.message,
    filename: event.filename,
    lineno: event.lineno,
    colno: event.colno,
    error: event.error
  })
})
;(async () => {
  // 清理可能损坏的 localStorage 数据（不包含theme-mode，避免清理主题设置）
  try {
    cleanupCorruptedStorage(['active_app', 'user-info', 'locale'])
  } catch (error) {
    console.warn('Failed to cleanup localStorage:', error)
  }

  // 提前初始化主题，避免页面闪烁
  const themeStore = useThemeStore()
  themeStore.initTheme()

  const token = document.cookie.replace(
    /(?:(?:^|.*;\s*)z_token\s*=\s*([^;]*).*$)|^.*$/,
    '$1'
  )
  if (token) {
    app.mount('#app')
  } else {
    const code = new URLSearchParams(window.location.search).get('code')
    if (code) {
      try {
        const res = await userApi.login({
          code: code
        })
        //set z_token to cookie
        document.cookie = `z_token=${res.data.token}; path=/`
        console.log('authApi', res)
        app.mount('#app')
      } catch (e) {
        // window.location.href = import.meta.env.VITE_OATH_URL
        console.log('login error:', e)
      }
    } else {
      window.location.href = import.meta.env.VITE_OATH_URL
    }
  }
})()
