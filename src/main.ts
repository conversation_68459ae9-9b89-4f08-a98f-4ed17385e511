import './assets/main.less'

import { createApp } from 'vue'
import { createPinia } from 'pinia'

import App from './App.vue'
import router from './router'
import ArcoVue from '@arco-design/web-vue'
import '@arco-design/web-vue/dist/arco.css'
import { userApi } from '@/networking/api'
import res_auth from '@/networking/interceptors/res_auth'
import req_auth from '@/networking/interceptors/req_auth'
import { rClient } from './networking/api/basicClient'

rClient.setBaseURL(import.meta.env.VITE_COMMON_ENDPOINT)
rClient.setRequestInterceptors([req_auth])
rClient.setResponseInterceptors([res_auth])

const app = createApp(App)

app.use(createPinia())
app.use(router)
app.use(ArcoVue)
;(async () => {
  const token = document.cookie.replace(
    /(?:(?:^|.*;\s*)z_token\s*=\s*([^;]*).*$)|^.*$/,
    '$1'
  )
  if (token) {
    app.mount('#app')
  } else {
    const code = new URLSearchParams(window.location.search).get('code')
    if (code) {
      try {
        const res = await userApi.login({
          code: code
        })
        //set z_token to cookie
        document.cookie = `z_token=${res.data.token}; path=/`
        console.log('authApi', res)
        app.mount('#app')
      } catch (e) {
        // window.location.href = import.meta.env.VITE_OATH_URL
        console.log('login error:', e)
      }
    } else {
      window.location.href = import.meta.env.VITE_OATH_URL
    }
  }
})()
