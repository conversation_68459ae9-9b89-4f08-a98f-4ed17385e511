import { createRouter, createWebHashHistory, createWeb<PERSON>istory } from 'vue-router'
import Layout from '@/views/layout/Layout.vue'
import ApiKeyManage from '@/views/apiKeyManage/ApiKeyManage.vue'
import Welcome from '@/views/welcome/Welcome.vue'
import Apps from '@/views/apps/Apps.vue'
import Publish from '@/views/publish/Publish.vue'
import Packages from '@/views/packages/Packages.vue'

const router = createRouter({
  history: createWebHashHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      component: Layout,
      redirect: '/home',
      children: [
        {
          path: '/home',
          name: 'home',
          component: Welcome
        },
        {
          path: '/apikey',
          name: 'apikey',
          component: ApiKeyManage
        },
        {
          path: '/apps',
          name: 'apps',
          component: Apps
        },
        {
          path: '/publish',
          name: 'publish',
          component: Publish
        }
      ]
    }
  ]
})

export default router
