<template>
  <div class="shimmer-demo-container">
    <header
      :class="[
        'sticky top-0 z-10 bg-white dark:bg-dark-primary border-b border-gray-200 dark:border-gray-600 p-6 transition-colors duration-300'
      ]"
    >
      <h1 :class="['text-2xl font-bold', isDark ? 'text-white' : 'text-gray-900']">
        Shimmer 效果演示
      </h1>
      <p :class="['mt-2 text-sm', isDark ? 'text-gray-300' : 'text-gray-600']">
        展示不同类型的加载骨架屏效果
      </p>
    </header>

    <main class="p-6 space-y-8">
      <!-- 控制面板 -->
      <section
        :class="[
          'bg-white dark:bg-dark-secondary rounded-lg shadow-sm border border-gray-200 dark:border-gray-600 p-6 transition-colors duration-300'
        ]"
      >
        <h2
          :class="[
            'text-lg font-semibold mb-4',
            isDark ? 'text-white' : 'text-gray-900'
          ]"
        >
          控制面板
        </h2>
        <div class="flex flex-wrap gap-4">
          <button
            v-for="(demo, key) in demos"
            :key="key"
            @click="toggleDemo(key)"
            :class="[
              'px-4 py-2 rounded-md text-sm font-medium transition-all duration-200',
              demo.active
                ? 'bg-blue-600 text-white'
                : isDark
                ? 'bg-dark-tertiary text-gray-300 hover:bg-dark-primary'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            ]"
          >
            {{ demo.active ? '停止' : '开始' }} {{ demo.name }}
          </button>
        </div>
      </section>

      <!-- 表格 Shimmer -->
      <section
        :class="[
          'bg-white dark:bg-dark-secondary rounded-lg shadow-sm border border-gray-200 dark:border-gray-600 p-6 transition-colors duration-300'
        ]"
      >
        <h2
          :class="[
            'text-lg font-semibold mb-4',
            isDark ? 'text-white' : 'text-gray-900'
          ]"
        >
          表格骨架屏
        </h2>
        <ShimmerLoading
          v-if="demos.table.active"
          type="table"
          :rows="6"
          :columns="5"
          :column-widths="['120px', '150px', '100px', '180px', '100px']"
        />
        <div
          v-else
          :class="[
            'border border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-8 text-center transition-colors duration-300'
          ]"
        >
          <p :class="['text-gray-500 dark:text-gray-400']">表格数据已加载完成</p>
        </div>
      </section>

      <!-- 卡片 Shimmer -->
      <section
        :class="[
          'bg-white dark:bg-dark-secondary rounded-lg shadow-sm border border-gray-200 dark:border-gray-600 p-6 transition-colors duration-300'
        ]"
      >
        <h2
          :class="[
            'text-lg font-semibold mb-4',
            isDark ? 'text-white' : 'text-gray-900'
          ]"
        >
          卡片骨架屏
        </h2>
        <ShimmerLoading v-if="demos.card.active" type="card" :cards="4" />
        <div
          v-else
          class="grid gap-6"
          style="grid-template-columns: repeat(auto-fill, minmax(300px, 1fr))"
        >
          <div
            v-for="i in 4"
            :key="i"
            :class="[
              'rounded-lg p-6 border transition-colors duration-300',
              isDark
                ? 'bg-dark-tertiary border-gray-600'
                : 'bg-gray-50 border-gray-200'
            ]"
          >
            <h3
              :class="[
                'font-semibold mb-2',
                isDark ? 'text-white' : 'text-gray-900'
              ]"
            >
              卡片标题 {{ i }}
            </h3>
            <p :class="['text-sm', isDark ? 'text-gray-300' : 'text-gray-600']">
              这里是卡片的内容描述。数据已经加载完成。
            </p>
          </div>
        </div>
      </section>

      <!-- 列表 Shimmer -->
      <section
        :class="[
          'bg-white dark:bg-dark-secondary rounded-lg shadow-sm border border-gray-200 dark:border-gray-600 p-6 transition-colors duration-300'
        ]"
      >
        <h2
          :class="[
            'text-lg font-semibold mb-4',
            isDark ? 'text-white' : 'text-gray-900'
          ]"
        >
          列表骨架屏
        </h2>
        <ShimmerLoading v-if="demos.list.active" type="list" :items="5" />
        <div v-else class="space-y-4">
          <div v-for="i in 5" :key="i" class="flex items-center space-x-4">
            <div
              :class="[
                'w-12 h-12 rounded-full flex items-center justify-center text-white font-semibold',
                `bg-${['blue', 'green', 'purple', 'red', 'yellow'][i - 1]}-500`
              ]"
            >
              {{ i }}
            </div>
            <div class="flex-1">
              <h3 :class="['font-medium', isDark ? 'text-white' : 'text-gray-900']">
                列表项 {{ i }}
              </h3>
              <p :class="['text-sm', isDark ? 'text-gray-300' : 'text-gray-600']">
                这是第 {{ i }} 个列表项的描述
              </p>
            </div>
          </div>
        </div>
      </section>

      <!-- 文本 Shimmer -->
      <section
        :class="[
          'bg-white dark:bg-dark-secondary rounded-lg shadow-sm border border-gray-200 dark:border-gray-600 p-6 transition-colors duration-300'
        ]"
      >
        <h2
          :class="[
            'text-lg font-semibold mb-4',
            isDark ? 'text-white' : 'text-gray-900'
          ]"
        >
          文本骨架屏
        </h2>
        <ShimmerLoading v-if="demos.text.active" type="text" :text-lines="6" />
        <div v-else class="space-y-3">
          <p
            :class="[
              'text-sm leading-relaxed',
              isDark ? 'text-gray-300' : 'text-gray-600'
            ]"
          >
            这是一段示例文本。当我们需要加载文本内容时，可以使用文本骨架屏来提供更好的用户体验。
          </p>
          <p
            :class="[
              'text-sm leading-relaxed',
              isDark ? 'text-gray-300' : 'text-gray-600'
            ]"
          >
            骨架屏能够让用户提前感知页面的大致结构，减少等待时的焦虑感。
          </p>
          <p
            :class="[
              'text-sm leading-relaxed',
              isDark ? 'text-gray-300' : 'text-gray-600'
            ]"
          >
            文本已经加载完成。
          </p>
        </div>
      </section>

      <!-- 自定义 Shimmer -->
      <section
        :class="[
          'bg-white dark:bg-dark-secondary rounded-lg shadow-sm border border-gray-200 dark:border-gray-600 p-6 transition-colors duration-300'
        ]"
      >
        <h2
          :class="[
            'text-lg font-semibold mb-4',
            isDark ? 'text-white' : 'text-gray-900'
          ]"
        >
          自定义骨架屏
        </h2>
        <ShimmerLoading v-if="demos.custom.active" type="custom">
          <div class="flex space-x-4">
            <div
              :class="[
                'w-20 h-20 rounded-lg',
                isDark ? 'shimmer-dark' : 'shimmer-light'
              ]"
            ></div>
            <div class="flex-1 space-y-3">
              <div
                :class="[
                  'h-4 rounded-md',
                  isDark ? 'shimmer-dark' : 'shimmer-light'
                ]"
              ></div>
              <div
                :class="[
                  'h-4 w-3/4 rounded-md',
                  isDark ? 'shimmer-dark' : 'shimmer-light'
                ]"
              ></div>
              <div
                :class="[
                  'h-4 w-1/2 rounded-md',
                  isDark ? 'shimmer-dark' : 'shimmer-light'
                ]"
              ></div>
            </div>
          </div>
        </ShimmerLoading>
        <div
          v-else
          :class="[
            'border border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-8 text-center transition-colors duration-300'
          ]"
        >
          <p :class="['text-gray-500 dark:text-gray-400']">自定义内容已加载完成</p>
        </div>
      </section>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useThemeStore } from '@/stores/themeStore'
import { storeToRefs } from 'pinia'
import ShimmerLoading from '@/components/ShimmerLoading.vue'

const themeStore = useThemeStore()
const { isDark } = storeToRefs(themeStore)

const demos = ref({
  table: { name: '表格', active: false },
  card: { name: '卡片', active: false },
  list: { name: '列表', active: false },
  text: { name: '文本', active: false },
  custom: { name: '自定义', active: false }
})

const toggleDemo = (key: string) => {
  demos.value[key as keyof typeof demos.value].active =
    !demos.value[key as keyof typeof demos.value].active
}
</script>

<style scoped>
.shimmer-demo-container {
  @apply min-h-screen;
  background-color: var(--color-bg-secondary);
}

/* 为了演示，需要重新定义 shimmer 动画类 */
.shimmer-light,
.shimmer-dark {
  @apply relative overflow-hidden;
  animation: shimmer 2s infinite linear;
}

.shimmer-light {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
}

.shimmer-dark {
  background: linear-gradient(90deg, #2d3748 25%, #4a5568 50%, #2d3748 75%);
  background-size: 200% 100%;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}
</style>
