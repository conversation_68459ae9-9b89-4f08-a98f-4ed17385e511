.layout {
  height: 100vh;
  width: 100vw;
  overflow: hidden;
  background: #ddd;
  .layout__header {
    display: flex;
    flex-direction: row;
    width: 100%;
  }
  .layout__menu {
  }
  .layout__title {
    color: white;
    width: 200px;
    //文字居中
    text-align: center;
    //垂直居中
    line-height: 60px;
    //字体大小
    font-size: 20px;
    background: #4b7bff;
  }
  .layout__login-info {
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    color: white;
    width: 200px;
    //文字居中
    text-align: center;
    //垂直居中
    line-height: 60px;
    //字体大小
    font-size: 14px;
    background: #4b7bff;
  }
}

/* 侧边栏优化动画 */
.sidebar-slide-enter-active {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar-slide-leave-active {
  transition: transform 0.25s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar-slide-enter-from {
  transform: translateX(-100%);
}

.sidebar-slide-leave-to {
  transform: translateX(-100%);
}

/* 背景遮罩动画 */
.backdrop-fade-enter-active {
  transition: opacity 0.3s ease-out;
}

.backdrop-fade-leave-active {
  transition: opacity 0.2s ease-in;
}

.backdrop-fade-enter-from,
.backdrop-fade-leave-to {
  opacity: 0;
}

/* 菜单项悬停效果增强 */
.menu-item {
  position: relative;
  overflow: hidden;
}

.menu-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  transition: left 0.5s ease-in-out;
}

.menu-item:hover::before {
  left: 100%;
}

/* 侧边栏阴影效果 */
.sidebar-shadow {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05),
    0 0 0 1px rgba(255, 255, 255, 0.05);
}

/* 移动端优化 */
@media (max-width: 1024px) {
  .sidebar-mobile {
    will-change: transform;
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
  }
}

/* 滚动条美化 */
.sidebar-scrollbar::-webkit-scrollbar {
  width: 4px;
}

.sidebar-scrollbar::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
}

.sidebar-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
}

.sidebar-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* 菜单项激活状态指示器 */
.menu-item-active {
  position: relative;
}

.menu-item-active::after {
  content: '';
  position: absolute;
  left: -24px;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 24px;
  background: #3b82f6;
  border-radius: 0 2px 2px 0;
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-50%) scaleY(0);
  }
  to {
    opacity: 1;
    transform: translateY(-50%) scaleY(1);
  }
}

/* 按钮点击反馈 */
.button-feedback {
  position: relative;
  overflow: hidden;
}

.button-feedback::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.button-feedback:active::after {
  width: 300px;
  height: 300px;
}

/* 子路由切换 fade 动画 */
.fade-enter-active {
  transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
}

.fade-leave-active {
  transition: opacity 0.2s ease-in-out, transform 0.2s ease-in-out;
}

.fade-enter-from {
  opacity: 0;
}

.fade-enter-to {
  opacity: 1;
}

.fade-leave-from {
  opacity: 1;
}

.fade-leave-to {
  opacity: 0;
}
