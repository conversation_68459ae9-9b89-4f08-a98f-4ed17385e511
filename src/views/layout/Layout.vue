<template>
  <div
    :class="[
      'transition-colors duration-300',
      isDark ? 'bg-dark-primary' : 'bg-white'
    ]"
  >
    <TransitionRoot as="template" :show="sidebarOpen">
      <Dialog as="div" class="relative z-50 lg:hidden" @close="closeSidebar">
        <TransitionChild
          as="template"
          enter="transition-opacity ease-out duration-300"
          enter-from="opacity-0"
          enter-to="opacity-100"
          leave="transition-opacity ease-in duration-200"
          leave-from="opacity-100"
          leave-to="opacity-0"
        >
          <div
            :class="[
              'fixed inset-0 backdrop-blur-sm transition-colors duration-300',
              isDark ? 'bg-dark-primary/90' : 'bg-gray-900/80'
            ]"
          />
        </TransitionChild>

        <div class="fixed inset-0 flex">
          <TransitionChild
            as="template"
            enter="transition ease-out duration-300 transform"
            enter-from="-translate-x-full"
            enter-to="translate-x-0"
            leave="transition ease-in duration-250 transform"
            leave-from="translate-x-0"
            leave-to="-translate-x-full"
          >
            <DialogPanel class="relative mr-16 flex w-full max-w-xs flex-1">
              <TransitionChild
                as="template"
                enter="ease-out duration-300 delay-100"
                enter-from="opacity-0"
                enter-to="opacity-100"
                leave="ease-in duration-200"
                leave-from="opacity-100"
                leave-to="opacity-0"
              >
                <div class="absolute left-full top-0 flex w-16 justify-center pt-5">
                  <button
                    type="button"
                    class="-m-2.5 p-2.5 rounded-full hover:bg-white/10 transition-colors duration-200"
                    @click="closeSidebar"
                  >
                    <span class="sr-only">Close sidebar</span>
                    <X class="h-6 w-6 text-white" aria-hidden="true" />
                  </button>
                </div>
              </TransitionChild>
              <!-- Mobile Sidebar - 使用 aside 语义化标签 -->
              <aside
                :class="[
                  'flex grow flex-col gap-y-5 overflow-y-auto px-6 pb-4 shadow-xl sidebar-shadow sidebar-mobile sidebar-scrollbar transition-colors duration-300',
                  isDark ? 'bg-dark-secondary' : 'bg-gray-900'
                ]"
                role="navigation"
                aria-label="主导航菜单"
              >
                <header
                  class="flex h-16 shrink-0 items-center cursor-pointer transition-transform duration-200 hover:scale-105"
                  @click.stop="onMenuClick('/welcome')"
                >
                  <img
                    class="h-12 w-auto"
                    src="@/assets/logo.webp"
                    alt="可意升级管理后台"
                  />
                </header>
                <nav
                  class="flex flex-1 flex-col"
                  role="navigation"
                  aria-label="主要功能导航"
                >
                  <ul role="list" class="flex flex-1 flex-col gap-y-7">
                    <li>
                      <ul role="list" class="-mx-2 space-y-1">
                        <li v-for="item in navigation" :key="item.name">
                          <div
                            @click.stop="onMenuClick(item.href)"
                            :class="[
                              routerManager.currentRoute.value.path == item.href
                                ? isDark
                                  ? 'bg-dark-tertiary text-white scale-105 shadow-md menu-item-active'
                                  : 'bg-gray-800 text-white scale-105 shadow-md menu-item-active'
                                : isDark
                                ? 'text-gray-300 hover:text-white hover:bg-dark-tertiary hover:scale-105'
                                : 'text-gray-400 hover:text-white hover:bg-gray-800 hover:scale-105',
                              'group flex gap-x-3 rounded-md p-3 text-sm leading-6 font-semibold cursor-pointer transition-all duration-200 ease-out transform menu-item button-feedback'
                            ]"
                            role="menuitem"
                            :aria-current="
                              routerManager.currentRoute.value.path == item.href
                                ? 'page'
                                : undefined
                            "
                          >
                            <component
                              :is="item.icon"
                              :class="[
                                routerManager.currentRoute.value.path == item.href
                                  ? 'text-white'
                                  : isDark
                                  ? 'text-gray-300 group-hover:text-white'
                                  : 'text-gray-400 group-hover:text-white',
                                'h-6 w-6 shrink-0 transition-colors duration-200'
                              ]"
                              aria-hidden="true"
                            />
                            {{ item.name }}
                          </div>
                        </li>
                      </ul>
                    </li>
                    <!-- 主题切换区域 -->
                    <li class="mt-auto">
                      <ThemeToggle :show-dropdown="true" />
                    </li>
                    <!-- 用户信息和退出登录区域 -->
                    <li>
                      <Menu as="div" class="relative">
                        <MenuButton
                          :class="[
                            'group -mx-2 flex w-full items-center gap-x-3 rounded-md p-3 text-sm font-semibold leading-6 button-feedback transition-colors duration-200',
                            isDark
                              ? 'text-white hover:bg-dark-tertiary'
                              : 'text-white hover:bg-gray-800'
                          ]"
                        >
                          <img
                            class="h-8 w-8 rounded-full bg-gray-50"
                            src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80"
                            alt="用户头像"
                          />
                          <span class="truncate">{{ userName }}</span>
                          <ChevronUp
                            :class="[
                              'ml-auto h-5 w-5 shrink-0 group-hover:text-white ui-open:rotate-180 ui-open:transform transition-transform',
                              isDark ? 'text-gray-300' : 'text-gray-400'
                            ]"
                            aria-hidden="true"
                          />
                        </MenuButton>
                        <transition
                          enter-active-class="transition ease-out duration-100"
                          enter-from-class="transform opacity-0 scale-95"
                          enter-to-class="transform opacity-100 scale-100"
                          leave-active-class="transition ease-in duration-75"
                          leave-from-class="transform opacity-100 scale-100"
                          leave-to-class="transform opacity-0 scale-95"
                        >
                          <MenuItems
                            :class="[
                              'absolute bottom-full right-0 z-10 mb-2 w-56 origin-bottom-right rounded-md shadow-lg ring-1 focus:outline-none transition-colors duration-200',
                              isDark
                                ? 'bg-dark-tertiary ring-white/5'
                                : 'bg-gray-800 ring-white/5'
                            ]"
                          >
                            <div class="py-1">
                              <MenuItem v-slot="{ active }">
                                <button
                                  @click="logout"
                                  :class="[
                                    active
                                      ? isDark
                                        ? 'bg-dark-primary text-white'
                                        : 'bg-gray-700 text-white'
                                      : isDark
                                      ? 'text-gray-200'
                                      : 'text-gray-300',
                                    'group flex w-full items-center rounded-md px-4 py-2 text-sm transition-colors duration-200'
                                  ]"
                                >
                                  <LogOut
                                    :class="[
                                      'mr-2 h-5 w-5 group-hover:text-white transition-colors duration-200',
                                      isDark ? 'text-gray-300' : 'text-gray-400'
                                    ]"
                                    aria-hidden="true"
                                  />
                                  退出登录
                                </button>
                              </MenuItem>
                            </div>
                          </MenuItems>
                        </transition>
                      </Menu>
                    </li>
                  </ul>
                </nav>
              </aside>
            </DialogPanel>
          </TransitionChild>
        </div>
      </Dialog>
    </TransitionRoot>

    <!-- Desktop Sidebar - 使用 aside 语义化标签 -->
    <aside
      class="hidden lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-64 lg:flex-col"
      role="navigation"
      aria-label="主导航菜单"
    >
      <div
        :class="[
          'flex grow flex-col gap-y-5 overflow-y-auto px-6 pb-4 shadow-xl sidebar-shadow sidebar-scrollbar transition-colors duration-300',
          isDark ? 'bg-dark-secondary' : 'bg-gray-900'
        ]"
      >
        <header
          class="flex h-16 shrink-0 items-center cursor-pointer transition-transform duration-200 hover:scale-105"
          @click.stop="onMenuClick('/welcome')"
        >
          <img class="h-12 w-auto" src="@/assets/logo.webp" alt="可意升级管理后台" />
          <h1
            :class="[
              'text-xl font-bold leading-7 sm:truncate sm:text-base sm:tracking-tight p-4 transition-colors duration-300',
              isDark ? 'text-white' : 'text-white'
            ]"
          >
            可意升级管理后台
          </h1>
        </header>
        <nav
          class="flex flex-1 flex-col"
          role="navigation"
          aria-label="主要功能导航"
        >
          <ul role="list" class="flex flex-1 flex-col gap-y-7">
            <li>
              <ul role="list" class="-mx-2 space-y-1">
                <li v-for="item in navigation" :key="item.name">
                  <div
                    @click.stop="onMenuClick(item.href)"
                    :class="[
                      routerManager.currentRoute.value.path == item.href
                        ? isDark
                          ? 'bg-dark-tertiary text-white scale-105 shadow-md menu-item-active'
                          : 'bg-gray-800 text-white scale-105 shadow-md menu-item-active'
                        : isDark
                        ? 'text-gray-300 hover:text-white hover:bg-dark-tertiary hover:scale-105'
                        : 'text-gray-400 hover:text-white hover:bg-gray-800 hover:scale-105',
                      'group flex gap-x-3 rounded-md p-3 text-sm leading-6 font-semibold cursor-pointer transition-all duration-200 ease-out transform menu-item button-feedback'
                    ]"
                    role="menuitem"
                    :aria-current="
                      routerManager.currentRoute.value.path == item.href
                        ? 'page'
                        : undefined
                    "
                  >
                    <component
                      :is="item.icon"
                      :class="[
                        routerManager.currentRoute.value.path == item.href
                          ? 'text-white'
                          : isDark
                          ? 'text-gray-300 group-hover:text-white'
                          : 'text-gray-400 group-hover:text-white',
                        'h-6 w-6 shrink-0 transition-colors duration-200'
                      ]"
                      aria-hidden="true"
                    />
                    {{ item.name }}
                  </div>
                </li>
              </ul>
            </li>
            <!-- 主题切换区域 -->
            <li class="mt-auto">
              <ThemeToggle :show-dropdown="true" />
            </li>
            <!-- 用户信息和退出登录区域 -->
            <li>
              <Menu as="div" class="relative">
                <MenuButton
                  :class="[
                    'group -mx-2 flex w-full items-center gap-x-3 rounded-md p-3 text-sm font-semibold leading-6 button-feedback transition-colors duration-200',
                    isDark
                      ? 'text-white hover:bg-dark-tertiary'
                      : 'text-white hover:bg-gray-800'
                  ]"
                >
                  <img
                    class="h-8 w-8 rounded-full bg-gray-50"
                    src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80"
                    alt="用户头像"
                  />
                  <span class="truncate">{{ userName }}</span>
                  <ChevronUp
                    :class="[
                      'ml-auto h-5 w-5 shrink-0 group-hover:text-white ui-open:rotate-180 ui-open:transform transition-transform',
                      isDark ? 'text-gray-300' : 'text-gray-400'
                    ]"
                    aria-hidden="true"
                  />
                </MenuButton>
                <transition
                  enter-active-class="transition ease-out duration-100"
                  enter-from-class="transform opacity-0 scale-95"
                  enter-to-class="transform opacity-100 scale-100"
                  leave-active-class="transition ease-in duration-75"
                  leave-from-class="transform opacity-100 scale-100"
                  leave-to-class="transform opacity-0 scale-95"
                >
                  <MenuItems
                    :class="[
                      'absolute bottom-full right-0 z-10 mb-2 w-56 origin-bottom-right rounded-md shadow-lg ring-1 focus:outline-none transition-colors duration-200',
                      isDark
                        ? 'bg-dark-tertiary ring-white/5'
                        : 'bg-gray-800 ring-white/5'
                    ]"
                  >
                    <div class="py-1">
                      <MenuItem v-slot="{ active }">
                        <button
                          @click="logout"
                          :class="[
                            active
                              ? isDark
                                ? 'bg-dark-primary text-white'
                                : 'bg-gray-700 text-white'
                              : isDark
                              ? 'text-gray-200'
                              : 'text-gray-300',
                            'group flex w-full items-center rounded-md px-4 py-2 text-sm transition-colors duration-200'
                          ]"
                        >
                          <LogOut
                            :class="[
                              'mr-2 h-5 w-5 group-hover:text-white transition-colors duration-200',
                              isDark ? 'text-gray-300' : 'text-gray-400'
                            ]"
                            aria-hidden="true"
                          />
                          退出登录
                        </button>
                      </MenuItem>
                    </div>
                  </MenuItems>
                </transition>
              </Menu>
            </li>
          </ul>
        </nav>
      </div>
    </aside>

    <div class="lg:pl-64">
      <main
        :class="[
          'py-4 transition-colors duration-300',
          isDark ? 'bg-dark-primary' : 'bg-white'
        ]"
        role="main"
        aria-label="主要内容区域"
      >
        <div class="px-4 sm:px-6 lg:px-8">
          <RouterView v-slot="{ Component, route }">
            <Transition name="fade" mode="out-in">
              <component :is="Component" :key="route.path" />
            </Transition>
          </RouterView>
        </div>
      </main>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, watch, computed, Transition } from 'vue'
import { userApi } from '@/networking/api'
import {
  Dialog,
  DialogPanel,
  Menu,
  MenuButton,
  MenuItem,
  MenuItems,
  TransitionChild,
  TransitionRoot
} from '@headlessui/vue'
import {
  Key,
  Package,
  Rocket,
  Sparkles,
  Menu as MenuIcon,
  X,
  ChevronUp,
  LogOut,
  AppWindowMac,
  LucideLayoutGrid
} from 'lucide-vue-next'
import router from '@/router'
import { useRouter } from 'vue-router'
import { useSidebar } from '@/composables/useSidebar'
import { useThemeStore } from '@/stores/themeStore'
import ThemeToggle from '@/components/ThemeToggle.vue'
import { storeToRefs } from 'pinia'

const routerManager = useRouter()
const navigation = [
  { name: 'API Key 管理', href: '/apikey', icon: Key, current: false },
  { name: '应用管理', href: '/apps', icon:  LucideLayoutGrid, current: false },
  { name: '发布管理', href: '/publish', icon: Rocket, current: false },
  { name: '包管理', href: '/packages', icon: Package, current: false }
]

const { sidebarOpen, isAnimating, openSidebar, closeSidebar, toggleSidebar } =
  useSidebar()

// 主题管理
const themeStore = useThemeStore()
const { isDark } = storeToRefs(themeStore)

const userName = ref('')
const currentPage = ref('')

// 计算当前路由对应的导航名称
const getCurrentRouteName = computed(() => {
  const currentPath = routerManager.currentRoute.value.path
  const currentNav = navigation.find((item) => item.href === currentPath)
  return currentNav ? currentNav.name : '首页'
})

onMounted(async () => {
  const res = await userApi.getUserDetail()
  userName.value = res.data.display_name
  console.log('userName', userName.value)
})

const logout = async () => {
  document.cookie = 'z_token=; path=/;'
  const res = await userApi.logout()
  window.location.href = '/'
}

const onMenuClick = (href: string) => {
  console.log('onMenuClick href', href)
  currentPage.value = href
  router.push(href)
}
</script>

<style scoped lang="less">
@import './Layout.less';
</style>
