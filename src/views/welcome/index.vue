<template>
  <main class="grid min-h-full place-items-center px-6 py-24 sm:py-32 lg:px-8">
    <div class="text-center">
      <p
        :class="[
          'text-base font-semibold',
          isDark ? 'text-indigo-400' : 'text-indigo-600'
        ]"
      >
        欢迎使用禅聊升级后台
      </p>
      <h1
        :class="[
          'mt-4 text-3xl font-bold tracking-tight sm:text-5xl',
          isDark ? 'text-white' : 'text-gray-900'
        ]"
      >
        别乱点，求求了🙇🏻
      </h1>
    </div>
  </main>
</template>

<script setup lang="ts">
import { useThemeStore } from '@/stores/themeStore'
import { storeToRefs } from 'pinia'

const themeStore = useThemeStore()
const { isDark } = storeToRefs(themeStore)
</script>

<style scoped lang="less">
.card_container {
  position: relative;
  top: 20%;
  display: flex;
  justify-content: center;
}
</style>
