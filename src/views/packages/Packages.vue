<template>
  <div class="px-4 sm:px-6 lg:px-8">
    <div
      class="sticky top-0 z-40 flex h-12 shrink-0 items-center gap-x-4 border-b border-gray-200 bg-white px-4 lg:px-8"
    >
      <h3 class="text-base font-semibold leading-6 text-gray-900 sm:px-6">APP</h3>
      <div class="h-6 w-px bg-gray-900/10" aria-hidden="true" />
      <div class="flex flex-1 items-center gap-x-4 self-stretch lg:gap-x-6 sm:px-6">
        <Combobox as="div" v-model="activeApp">
          <div class="relative">
            <ComboboxInput
              class="w-full rounded-md border-0 bg-white py-1.5 pl-3 pr-10 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
              :display-value="(_) => activeApp.app_name"
            />
            <ComboboxButton
              class="absolute inset-y-0 right-0 flex items-center rounded-r-md px-2 focus:outline-none"
            >
              <ChevronUpDownIcon class="h-5 w-5 text-gray-400" aria-hidden="true" />
            </ComboboxButton>

            <ComboboxOptions
              v-if="apps.length > 0"
              class="fixed z-20 mt-1 max-h-60 w-60 overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm"
            >
              <ComboboxOption
                v-for="app in apps"
                :key="app.package_name"
                :value="app"
                as="template"
              >
                <li
                  :class="[
                    'relative cursor-default select-none py-2 pl-8 pr-4',
                    isSelectApp(app) ? 'bg-indigo-600 text-white' : 'text-gray-900'
                  ]"
                >
                  <span
                    :class="['block truncate', isSelectApp(app) && 'font-semibold']"
                  >
                    {{ app.app_name }}
                  </span>

                  <span
                    v-if="isSelectApp(app)"
                    :class="[
                      'absolute inset-y-0 left-0 flex items-center pl-1.5',
                      activeApp.package_name == app.package_name
                        ? 'text-white'
                        : 'text-indigo-600'
                    ]"
                  >
                  </span>
                </li>
              </ComboboxOption>
            </ComboboxOptions>
          </div>
        </Combobox>
      </div>
    </div>
    <div ref="ak_table" class="py-4">
      <a-table :data="data" :pagination="true" page-position="bottom">
        <template #columns>
          <a-table-column
            :ellipsis="true"
            :width="50"
            title="id"
            data-index="_id"
            :tooltip="true"
          ></a-table-column>
          <a-table-column
            :ellipsis="true"
            :width="80"
            title="package_name"
            data-index="package_name"
            :tooltip="true"
          ></a-table-column>
          <a-table-column
            :ellipsis="true"
            :width="50"
            title="platform"
            data-index="platform"
            :tooltip="true"
          ></a-table-column>
          <a-table-column
            :ellipsis="true"
            :width="50"
            title="version_code"
            data-index="version_code"
            :tooltip="true"
            :sortable="{
              sortDirections: ['descend', 'ascend'],
              defaultSortOrder: 'descend'
            }"
          ></a-table-column>
          <a-table-column
            :ellipsis="true"
            :width="50"
            title="version_name"
            data-index="version_name"
            :tooltip="true"
          ></a-table-column>
          <a-table-column
            :ellipsis="true"
            :width="50"
            title="channel"
            data-index="channel"
            :tooltip="true"
            :filterable="channelFilter"
          ></a-table-column>
          <a-table-column
            :ellipsis="true"
            :width="80"
            title="创建时间"
            data-index="create_time"
            :tooltip="true"
          ></a-table-column>
          <a-table-column :width="100" title="操作">
            <template #cell="{ record }">
              <a-space flex-auto>
                <a-button @click="handleDownloadClick(record)">下载</a-button>
              </a-space>
              <a-space flex-auto>
                <a-button status="danger" @click="handleClick(record)">
                  发布APP
                </a-button>
              </a-space>
            </template>
          </a-table-column>
        </template>
      </a-table>
    </div>
    <a-modal
      v-model:visible="configCreateVisible"
      @cancel="cancelCreateConfig"
      @before-ok="createConfig"
    >
      <template #title> 发布 {{ configCreateForm.package_id }} </template>
      <div>
        <a-form ref="createFormRef" :model="configCreateForm">
          <a-form-item field="upgrade_features" label="功能文案" :rules="descRules">
            <a-textarea
              placeholder="Please enter something"
              v-model="configCreateForm.upgrade_features"
              allow-clear
            />
          </a-form-item>
          <a-form-item field="update_type" label="升级类型">
            <a-select
              :style="{ width: '320px' }"
              placeholder="Please select ..."
              @change="handleOptionChange"
              :default-value="configCreateForm.update_type"
              v-model:model-value="configCreateForm.update_type"
            >
              <a-option v-for="item in [1, 0]" :key="item" :value="item">
                {{ item }}
              </a-option>
            </a-select>
          </a-form-item>
          <a-form-item field="package_downloadUrl" label="下载" :rules="descRules">
            <a-input v-model="configCreateForm.package_downloadUrl" />
          </a-form-item>
          <a-form-item field="official_site" label="官网" :rules="descRules">
            <a-input v-model="configCreateForm.official_site" />
          </a-form-item>
        </a-form>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { useAppsStore } from '@/stores/appsStore'
import { storeToRefs } from 'pinia'
import {
  Combobox,
  ComboboxButton,
  ComboboxInput,
  ComboboxLabel,
  ComboboxOption,
  ComboboxOptions
} from '@headlessui/vue'
import { appsApi } from '@/networking/api'
import { ChevronDownIcon, ChevronUpDownIcon } from '@heroicons/vue/20/solid'
import { onMounted, ref, watch } from 'vue'
import { tryOnMounted, useWindowSize } from '@vueuse/core'
import moment from 'dayjs'
import { isArray } from 'lodash'
import axios from 'axios'
import type { TableFilterable } from '@arco-design/web-vue/es/table'

const data = ref<any[]>([])
const store = useAppsStore()
const { activeApp, apps } = storeToRefs(store)

const { width: windowWidth, height: windowHeight } = useWindowSize()
const ak_table = ref(null as any)
const scroll = ref({
  y: 500
})

watch(windowHeight, () => {
  scroll.value.y = windowHeight.value - 165
})

onMounted(async () => {
  scroll.value.y = windowHeight.value - 165
  try {
    await store.reloadApps()
    await reloadUploadPackages()
  } catch (e) {
    console.log(e)
  }
})

function isSelectApp(app: any) {
  return (
    activeApp.value.package_name === app.package_name &&
    activeApp.value.platform === app.platform
  )
}

const channelFilter = ref({
  filters: [
    {
      text: '空',
      value: '0'
    },
    {
      text: 'VIP用户',
      value: 'vip'
    },
    {
      text: '管理员用户',
      value: 'admin'
    },
    {
      text: '普通用户',
      value: 'regular'
    }
  ],
  filter: (value: any, record: any) => record.channel == value,
  multiple: true
} as TableFilterable)

async function reloadUploadPackages() {
  if (activeApp.value) {
    const res = await appsApi.allUploadPackages(
      activeApp.value.package_name,
      activeApp.value.platform
    )
    console.log('reloadUploadPackages res', res)
    if (isArray(res.data)) {
      channelFilter.value.filters = []
      data.value = res.data.map((item: any) => {
        if (!channelFilter.value.filters!.includes(item.channel)) {
          channelFilter.value.filters!.push({
            text: item.channel,
            value: item.channel
          })
        }
        return {
          ...item,
          create_time: moment(item.create_time).format('YYYY-MM-DD HH:mm:ss')
        }
      })
    } else {
      data.value = []
    }
  }
}

watch(
  () => activeApp.value,
  (val) => {
    console.log('activeApp', val)
    store.setActiveApp(val)
    reloadUploadPackages()
  }
)

async function handleDownloadClick(record: any) {
  const res = await appsApi.getDownloadUrlByHash(record.file_id)
  console.log('handleDownloadClick', res.data)
  window.open(res.data)
}

function handleClick(record: any) {
  console.log('handleClick', record)
  configCreateVisible.value = true
  configCreateForm.value.package_id = record._id
  configCreateForm.value.upgrade_features = ''
  configCreateForm.value.update_type = 0
  configCreateForm.value.package_downloadUrl = ''
  configCreateForm.value.official_site = 'https://chat.zenmen.com/download/'
  configCreateForm.value.locale = 'zh'
}

const createFormRef = ref()
const configCreateVisible = ref(false)

const configCreateForm = ref({
  package_id: '',
  upgrade_features: '',
  update_type: 0,
  package_downloadUrl: '',
  official_site: 'https://chat.zenmen.com/download/',
  locale: 'zh'
})

const descRules = [
  {
    required: true,
    message: '必须填写'
  }
]

const cancelCreateConfig = () => {
  configCreateVisible.value = false
}

function handleOptionChange(value: any) {
  configCreateForm.value.update_type = parseInt(value)
  console.log('handleOptionChange category ', configCreateForm.value)
}

const createConfig = async (done: (success: boolean) => boolean | void) => {
  try {
    console.log('createConfig', configCreateForm.value)
    try {
      new URL(configCreateForm.value.package_downloadUrl)
    } catch (e) {
      createFormRef.value.setFields({
        package_downloadUrl: {
          status: 'error',
          message: '不是合法的URL地址'
        }
      })
      done(false)
      return
    }
    try {
      new URL(configCreateForm.value.official_site)
    } catch (e) {
      createFormRef.value.setFields({
        official_site: {
          status: 'error',
          message: '不是合法的URL地址'
        }
      })
      done(false)
      return
    }
    if (!configCreateForm.value.upgrade_features) {
      createFormRef.value.setFields({
        upgrade_features: {
          status: 'error',
          message: '不能为空'
        }
      })
      done(false)
      return
    }

    const res = await appsApi.publihPackage(
      configCreateForm.value.package_id,
      configCreateForm.value.upgrade_features,
      configCreateForm.value.update_type,
      configCreateForm.value.package_downloadUrl,
      configCreateForm.value.official_site,
      configCreateForm.value.locale
    )
    console.log('createConfig res', res)

    configCreateForm.value = {
      package_id: '',
      upgrade_features: '',
      update_type: 0,
      package_downloadUrl: '',
      official_site: 'https://chat.zenmen.com/download/',
      locale: 'zh'
    }
    done(true)
    await new Promise((resolve) => setTimeout(resolve, 200))
    done(true)
  } catch (e) {
    done(false)
    return
  }
}
</script>
<style scoped lang="less">
@import './Packages.less';
</style>
