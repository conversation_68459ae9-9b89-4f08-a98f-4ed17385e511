<template>
  <article>
    <div
      :class="[
        'sticky top-0 z-10 flex h-12 shrink-0 items-center justify-between transition-colors duration-300',
        isDark ? 'bg-dark-primary' : 'bg-white'
      ]"
    >
      <h1
        :class="[
          'text-xl font-semibold transition-colors duration-300',
          isDark ? 'text-white' : 'text-gray-900'
        ]"
      >
        包管理
      </h1>

      <div class="flex items-center gap-x-4">
        <Combobox as="div" v-model="activeApp">
          <div class="relative">
            <ComboboxInput
              :class="[
                'w-full rounded-md border-0 py-1.5 pl-3 pr-10 shadow-sm ring-1 ring-inset focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6 transition-colors duration-300',
                isDark
                  ? 'bg-dark-secondary text-white ring-dark-primary'
                  : 'bg-white text-gray-900 ring-gray-300'
              ]"
              :display-value="(app: any) => app?.app_name || ''"
              @change="query = $event.target.value"
              placeholder="搜索应用..."
            />
            <ComboboxButton
              class="absolute inset-y-0 right-0 flex items-center rounded-r-md px-2 focus:outline-none"
            >
              <ChevronDown
                :class="[
                  'h-5 w-5 transition-colors duration-300',
                  isDark ? 'text-gray-300' : 'text-gray-400'
                ]"
                aria-hidden="true"
              />
            </ComboboxButton>

            <ComboboxOptions
              v-if="apps.length > 0"
              :class="[
                'fixed z-20 mt-1 max-h-60 w-60 overflow-auto rounded-md py-1 text-base shadow-lg ring-1 ring-opacity-5 focus:outline-none sm:text-sm transition-colors duration-300',
                isDark ? 'bg-dark-secondary ring-black' : 'bg-white ring-black'
              ]"
            >
              <div
                v-if="query !== '' && filteredApps.length === 0"
                :class="[
                  'relative cursor-default select-none py-2 px-4',
                  isDark ? 'text-gray-400' : 'text-gray-500'
                ]"
              >
                没有找到匹配的应用
              </div>
              <ComboboxOption
                v-for="app in filteredApps"
                :key="app.package_name"
                :value="app"
                as="template"
              >
                <li
                  :class="[
                    'relative cursor-default select-none py-2 pl-8 pr-4 transition-colors duration-300',
                    isSelectApp(app)
                      ? 'bg-blue-600 text-white'
                      : isDark
                      ? 'text-white hover:bg-dark-tertiary'
                      : 'text-gray-900 hover:bg-gray-100'
                  ]"
                >
                  <span
                    :class="['block truncate', isSelectApp(app) && 'font-semibold']"
                  >
                    {{ app.app_name }}
                  </span>

                  <span
                    v-if="isSelectApp(app)"
                    :class="[
                      'absolute inset-y-0 left-0 flex items-center pl-1.5',
                      activeApp.package_name == app.package_name
                        ? 'text-white'
                        : 'text-blue-600'
                    ]"
                  >
                  </span>
                </li>
              </ComboboxOption>
            </ComboboxOptions>
          </div>
        </Combobox>
      </div>
    </div>
    <div ref="ak_table" class="py-4">
      <!-- 加载时显示 Shimmer 效果 -->
      <ShimmerLoading
        v-if="isPackagesLoading"
        type="table"
        :rows="6"
        :columns="8"
        :column-widths="[
          '120px',
          '150px',
          '80px',
          '100px',
          '120px',
          '80px',
          '140px',
          '180px'
        ]"
        container-class="mb-4"
      />

      <!-- 数据表格 -->
      <a-table
        v-else
        :bordered="false"
        :data="data"
        :pagination="true"
        page-position="bottom"
        :scroll="scroll"
        :scroll-x="true"
      >
        <template #columns>
          <a-table-column
            :ellipsis="true"
            :width="120"
            title="包ID"
            data-index="_id"
            :tooltip="true"
          >
            <template #cell="{ record }">
              <span class="font-mono text-sm">{{ record._id.slice(-8) }}</span>
            </template>
          </a-table-column>
          <a-table-column
            :ellipsis="true"
            :width="150"
            title="包名"
            data-index="package_name"
            :tooltip="true"
          >
            <template #cell="{ record }">
              <div class="flex flex-col">
                <span class="font-medium">{{ record.package_name }}</span>
                <span class="text-xs text-gray-500">{{ record.platform }}</span>
              </div>
            </template>
          </a-table-column>
          <a-table-column
            :ellipsis="true"
            :width="80"
            title="平台"
            data-index="platform"
            :tooltip="true"
          >
            <template #cell="{ record }">
              <a-tag
                :color="record.platform === 'android' ? 'green' : record.platform === 'ios' ? 'blue' : 'gray'"
              >
                {{ record.platform }}
              </a-tag>
            </template>
          </a-table-column>
          <a-table-column
            :ellipsis="true"
            :width="100"
            title="版本"
            data-index="version_code"
            :tooltip="true"
            :sortable="{
              sortDirections: ['descend', 'ascend'],
              defaultSortOrder: 'descend'
            }"
          >
            <template #cell="{ record }">
              <div class="flex items-center">
                <span class="font-medium">{{ record.version_name }}</span>
                <span class="ml-2 text-gray-500">({{ record.version_code }})</span>
              </div>
            </template>
          </a-table-column>
          <a-table-column
            :ellipsis="true"
            :width="120"
            title="版本名称"
            data-index="version_name"
            :tooltip="true"
          ></a-table-column>
          <a-table-column
            :ellipsis="true"
            :width="80"
            title="渠道"
            data-index="channel"
            :tooltip="true"
            :filterable="channelFilter"
          >
            <template #cell="{ record }">
              <a-tag v-if="record.channel" bordered>
                {{ record.channel }}
              </a-tag>
              <span v-else class="text-gray-400">-</span>
            </template>
          </a-table-column>
          <a-table-column
            :ellipsis="true"
            :width="140"
            title="创建时间"
            data-index="create_time"
            :tooltip="true"
          ></a-table-column>
          <a-table-column :width="180" title="操作">
            <template #cell="{ record }">
              <a-space>
                <a-button
                  size="mini"
                  :class="[
                    'transition-colors duration-200',
                    isDark ? 'bg-blue-900/60 text-blue-100 hover:bg-blue-800/70' : ''
                  ]"
                  type="primary"
                  @click="handleDownloadClick(record)"
                >
                  <template #icon>
                    <Download class="h-3 w-3" />
                  </template>
                  下载
                </a-button>
                <a-button
                  size="mini"
                  status="danger"
                  :class="[
                    'transition-colors duration-200',
                    isDark ? 'bg-red-900/60 text-red-100 hover:bg-red-800/70' : ''
                  ]"
                  @click="handleClick(record)"
                >
                  <template #icon>
                    <Rocket class="h-3 w-3" />
                  </template>
                  发布
                </a-button>
              </a-space>
            </template>
          </a-table-column>
        </template>
      </a-table>
    </div>
    <a-modal
      v-model:visible="configCreateVisible"
      @cancel="cancelCreateConfig"
      @before-ok="createConfig"
      width="600px"
    >
      <template #title>
        <div class="flex items-center gap-2">
          <Rocket class="h-5 w-5 text-blue-600" />
          <span>发布包到应用商店</span>
        </div>
      </template>
      <div class="py-2">
        <a-form ref="createFormRef" :model="configCreateForm" layout="vertical">
          <a-form-item field="upgrade_features" label="更新特性" :rules="descRules">
            <a-textarea
              v-model="configCreateForm.upgrade_features"
              placeholder="请输入本次更新的功能特性和改进内容..."
              :rows="4"
              allow-clear
              show-word-limit
              :max-length="500"
            />
          </a-form-item>
          <a-form-item field="update_type" label="更新类型">
            <a-radio-group v-model="configCreateForm.update_type" type="button">
              <a-radio :value="0">
                <template #icon>
                  <icon-check-circle-fill v-if="configCreateForm.update_type === 0" />
                </template>
                普通更新
              </a-radio>
              <a-radio :value="1">
                <template #icon>
                  <icon-exclamation-circle-fill v-if="configCreateForm.update_type === 1" />
                </template>
                强制更新
              </a-radio>
            </a-radio-group>
            <div class="text-gray-500 text-sm mt-2">
              <p>更新类型说明：</p>
              <ul class="list-disc pl-4 mt-1">
                <li>普通更新：用户可以选择是否更新</li>
                <li>强制更新：用户必须更新才能继续使用</li>
              </ul>
            </div>
          </a-form-item>
          <a-form-item field="package_downloadUrl" label="下载地址" :rules="descRules">
            <a-input
              v-model="configCreateForm.package_downloadUrl"
              placeholder="请输入应用包的下载地址"
            >
              <template #prefix>
                <Link class="h-4 w-4 text-gray-400" />
              </template>
            </a-input>
          </a-form-item>
          <a-form-item field="official_site" label="官网地址" :rules="descRules">
            <a-input
              v-model="configCreateForm.official_site"
              placeholder="请输入应用的官方网站地址"
            >
              <template #prefix>
                <Globe class="h-4 w-4 text-gray-400" />
              </template>
            </a-input>
          </a-form-item>
        </a-form>
      </div>
    </a-modal>
  </article>
</template>

<script setup lang="ts">
import { useAppsStore } from '@/stores/appsStore'
import { storeToRefs } from 'pinia'
import {
  Combobox,
  ComboboxButton,
  ComboboxInput,
  ComboboxOption,
  ComboboxOptions
} from '@headlessui/vue'
import { appsApi } from '@/networking/api'
import { ChevronDoubleUpIcon, ChevronUpDownIcon } from '@heroicons/vue/20/solid'
import { onMounted, ref, watch, computed } from 'vue'
import { useWindowSize } from '@vueuse/core'
import moment from 'dayjs'
import { isArray } from 'lodash'
import type { TableFilterable } from '@arco-design/web-vue/es/table'
import { useThemeStore } from '@/stores/themeStore'
import { Download, Rocket, Link, Globe, ChevronDown } from 'lucide-vue-next'
import {
  IconCheckCircleFill,
  IconExclamationCircleFill
} from '@arco-design/web-vue/es/icon'
import ShimmerLoading from '@/components/ShimmerLoading.vue'

const data = ref<any[]>([])
const store = useAppsStore()
const { activeApp, apps } = storeToRefs(store)

// 主题管理
const themeStore = useThemeStore()
const { isDark } = storeToRefs(themeStore)

// 搜索和过滤
const query = ref('')
const isPackagesLoading = ref(false)

// 计算过滤后的应用列表
const filteredApps = computed(() => {
  return query.value === ''
    ? apps.value
    : apps.value.filter((app: any) =>
        app.app_name.toLowerCase().includes(query.value.toLowerCase())
      )
})

const { width: windowWidth, height: windowHeight } = useWindowSize()
const ak_table = ref(null as any)
const scroll = ref({
  y: 500
})

watch(windowHeight, () => {
  scroll.value.y = windowHeight.value - 165
})

onMounted(async () => {
  scroll.value.y = windowHeight.value - 165
  try {
    await store.reloadApps()
    await reloadUploadPackages()
  } catch (e) {
    console.log(e)
  }
})

function isSelectApp(app: any) {
  return (
    activeApp.value?.package_name === app?.package_name &&
    activeApp.value?.platform === app?.platform
  )
}

const channelFilter = ref({
  filters: [
    {
      text: '空',
      value: '0'
    },
    {
      text: 'VIP用户',
      value: 'vip'
    },
    {
      text: '管理员用户',
      value: 'admin'
    },
    {
      text: '普通用户',
      value: 'regular'
    }
  ],
  filter: (value: any, record: any) => record.channel == value,
  multiple: true
} as TableFilterable)

async function reloadUploadPackages() {
  if (activeApp.value && 'package_name' in activeApp.value && 'platform' in activeApp.value) {
    try {
      isPackagesLoading.value = true
      const res = await appsApi.allUploadPackages({
        package_name: (activeApp.value as any).package_name,
        platform: (activeApp.value as any).platform
      })
      console.log('reloadUploadPackages res', res)
      if (isArray(res.data)) {
        channelFilter.value.filters = []
        data.value = res.data.map((item: any) => {
          if (!channelFilter.value.filters!.includes(item.channel)) {
            channelFilter.value.filters!.push({
              text: item.channel,
              value: item.channel
            })
          }
          return {
            ...item,
            create_time: moment(item.create_time).format('YYYY-MM-DD HH:mm:ss')
          }
        })
      } else {
        data.value = []
      }
    } catch (error) {
      console.error('Failed to load packages:', error)
      data.value = []
    } finally {
      isPackagesLoading.value = false
    }
  }
}

watch(
  () => activeApp.value,
  (val) => {
    console.log('activeApp', val)
    store.setActiveApp(val)
    reloadUploadPackages()
  }
)

async function handleDownloadClick(record: any) {
  const res = await appsApi.getDownloadUrlByHash(record.file_id)
  console.log('handleDownloadClick', res.data)
  const downloadUrl = typeof res.data === 'string' ? res.data : (res.data as any)?.downloadUrl
  if (downloadUrl) {
    window.open(downloadUrl)
  }
}

function handleClick(record: any) {
  console.log('handleClick', record)
  configCreateVisible.value = true
  configCreateForm.value.package_id = record._id
  configCreateForm.value.upgrade_features = ''
  configCreateForm.value.update_type = 0
  configCreateForm.value.package_downloadUrl = ''
  configCreateForm.value.official_site = 'https://chat.zenmen.com/download/'
  configCreateForm.value.locale = 'zh'
}

const createFormRef = ref()
const configCreateVisible = ref(false)

const configCreateForm = ref({
  package_id: '',
  upgrade_features: '',
  update_type: 0,
  package_downloadUrl: '',
  official_site: 'https://chat.zenmen.com/download/',
  locale: 'zh'
})

const descRules = [
  {
    required: true,
    message: '必须填写'
  }
]

const cancelCreateConfig = () => {
  configCreateVisible.value = false
}



const createConfig = async (done: (success: boolean) => boolean | void) => {
  try {
    console.log('createConfig', configCreateForm.value)
    try {
      new URL(configCreateForm.value.package_downloadUrl)
    } catch (e) {
      createFormRef.value.setFields({
        package_downloadUrl: {
          status: 'error',
          message: '不是合法的URL地址'
        }
      })
      done(false)
      return
    }
    try {
      new URL(configCreateForm.value.official_site)
    } catch (e) {
      createFormRef.value.setFields({
        official_site: {
          status: 'error',
          message: '不是合法的URL地址'
        }
      })
      done(false)
      return
    }
    if (!configCreateForm.value.upgrade_features) {
      createFormRef.value.setFields({
        upgrade_features: {
          status: 'error',
          message: '不能为空'
        }
      })
      done(false)
      return
    }

    const res = await appsApi.publishPackage({
      package_id: configCreateForm.value.package_id,
      upgrade_features: configCreateForm.value.upgrade_features,
      update_type: configCreateForm.value.update_type,
      package_downloadUrl: configCreateForm.value.package_downloadUrl,
      official_site: configCreateForm.value.official_site,
      locale: configCreateForm.value.locale
    })
    console.log('createConfig res', res)

    configCreateForm.value = {
      package_id: '',
      upgrade_features: '',
      update_type: 0,
      package_downloadUrl: '',
      official_site: 'https://chat.zenmen.com/download/',
      locale: 'zh'
    }
    done(true)
    await new Promise((resolve) => setTimeout(resolve, 200))
    done(true)
  } catch (e) {
    done(false)
    return
  }
}
</script>
<style scoped lang="less">
@import './index.less';
</style>
