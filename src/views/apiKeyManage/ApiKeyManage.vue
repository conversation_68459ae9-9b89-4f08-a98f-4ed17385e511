<template>
  <article>
    <div
      :class="[
        'sticky top-0 z-10 flex h-12 shrink-0 items-center justify-between transition-colors duration-300',
        isDark ? 'bg-dark-primary' : 'bg-white'
      ]"
    >
      <h1 :class="[
        'text-xl font-semibold transition-colors duration-300',
        isDark ? 'text-white' : 'text-gray-900'
      ]">API Key 管理</h1>
      <button
        type="button"
        :class="[
          'block rounded-md px-3 py-2 text-center text-sm font-semibold text-white shadow-sm transition-all duration-200 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2',
          isDark
            ? 'bg-blue-600 hover:bg-blue-500 focus-visible:outline-blue-500'
            : 'bg-blue-600 hover:bg-blue-500 focus-visible:outline-indigo-600'
        ]"
        @click="showApiKeyCreate"
        aria-label="创建新API Key"
      >
        创建API Key
      </button>
    </div>
    <div ref="ak_table" class="ak_table py-4">
      <a-table
        :bordered="false"
        :data="data"
        :pagination="true"
        page-position="bottom"
        :scroll="scroll"
        :scroll-x="true"
      >
        <template #columns>
          <a-table-column
            :ellipsis="true"
            :width="50"
            title="描述"
            data-index="description"
            :tooltip="true"
          ></a-table-column>
          <a-table-column :width="200" title="key" data-index="key"></a-table-column>
          <a-table-column
            :ellipsis="true"
            :width="80"
            title="创建时间"
            data-index="create_time"
            :tooltip="true"
            :sortable="{
              sortDirections: ['descend', 'ascend'],
              defaultSortOrder: 'descend'
            }"
          ></a-table-column>
          <a-table-column :width="50" title="操作">
            <template #cell="{ record }">
              <a-button status="danger" @click="showDeleteDialog(record)"
                >删除</a-button
              >
            </template>
          </a-table-column>
        </template>
        <!--      <a-table-column title="创建时间" dataIndex="createdAt" key="createdAt"></a-table-column>-->
        <!--      <a-table-column title="操作" key="action">-->
        <!--        <template #default="scope">-->
        <!--          <a-button type="link" @click="deleteApiKey(scope.record.id)">删除</a-button>-->
        <!--        </template>-->
        <!--      </a-table-column> -->
      </a-table>
    </div>

    <!-- 删除确认对话框 -->
    <DeleteConfirmDialog
      v-model:visible="deleteVisible"
      :title="'删除API Key确认'"
      :content="deleteWarning"
      @confirm="handleDeleteConfirm"
      @cancel="handleDeleteCancel"
    />

    <a-modal
      v-model:visible="apiKeyCreateVisible"
      @cancel="cancleCreateApiKey"
      @before-ok="createApiKey"
    >
      <template #title> 创建apiKey </template>
      <div>
        <a-form ref="formRef" :model="apiKeyCreateForm">
          <a-form-item field="description" label="描述" :rules="descRules">
            <a-input v-model="apiKeyCreateForm.description" />
          </a-form-item>
        </a-form>
      </div>
    </a-modal>
  </article>
</template>
<script setup lang="ts">
import { onMounted, reactive, ref, watch } from 'vue'
import { userApi } from '@/networking/api'
import moment from 'dayjs'
import { tryOnMounted, useWindowSize } from '@vueuse/core'
import DeleteConfirmDialog from '@/components/DeleteConfirmDialog.vue'
import { useThemeStore } from '@/stores/themeStore'
import { storeToRefs } from 'pinia'

// 主题管理
const themeStore = useThemeStore()
const { isDark } = storeToRefs(themeStore)

const { width: windowWidth, height: windowHeight } = useWindowSize()
const ak_table = ref(null as any)
const scroll = ref({
  y: 500
})

watch(windowHeight, () => {
  scroll.value.y = Math.max(windowHeight.value - 165, 300)
})

onMounted(async () => {
  scroll.value.y = Math.max(windowHeight.value - 165, 300)
  try {
    await requestApiKeyList()
  } catch (e) {
    console.log(e)
  }
})

const formRef = ref()
const descRules = [
  {
    required: true,
    message: '必须填描述'
    // validator: (value: any, cb: any) => {
    //   return new Promise((resolve, reject) => {
    //     if (value && value.length > 1 && value.length <= 30) {
    //       resolve(true);
    //     } else {
    //       formRef.value.setFields({
    //         description: {
    //           status: 'error',
    //           message: '描述文字长度得在2到30之间'
    //         }
    //       })
    //       reject('描述文字长度得在2到30之间');
    //     }
    //   })
    // }
  }
]
const apiKeyCreateVisible = ref(false)
const apiKeyCreateForm = reactive({
  description: ''
})
const showApiKeyCreate = () => {
  apiKeyCreateVisible.value = true
}

const createApiKey = async (done: (success: boolean) => boolean | void) => {
  try {
    const ret = await formRef.value.validate((err: any) => {})
    if (ret) throw new Error(`${ret.description.message}`)
    const res = await userApi.createApiKey({
      description: apiKeyCreateForm.description
    })
    console.log(res)
    await requestApiKeyList()
    done(true)
  } catch (e) {
    done(false)
    return
  }
}
const cancleCreateApiKey = () => {
  apiKeyCreateVisible.value = false
}
const data = ref<any[]>([])
const requestApiKeyList = async () => {
  const res = await userApi.getAllApiKeys()
  data.value = res.data.map((item: any) => {
    return {
      ...item,
      create_time: moment(item.create_time).format('YYYY-MM-DD HH:mm:ss')
    }
  })
  console.log(data)
}
const deleteKey = async (record: any) => {
  try {
    console.log(record)
    await userApi.deleteKey({ key: record.key })
    await requestApiKeyList()
  } catch (e) {
    console.log(e)
  }
}

const deleteVisible = ref(false)
const deleteWarning = ref('')
const showDeleteDialog = (record: any) => {
  deleteWarning.value = `确定要删除API Key "${record.description}"吗？`
  deleteVisible.value = true
}

const handleDeleteConfirm = async () => {
  try {
    await deleteKey(
      data.value.find((item) => item.key === deleteWarning.value.split('"')[1])
    )
    deleteVisible.value = false
  } catch (e) {
    console.log(e)
  }
}

const handleDeleteCancel = () => {
  deleteVisible.value = false
}
</script>

<style scoped lang="less">
@import './ApiKeyManage.less';
</style>
