<template>
  <article role="main" aria-labelledby="page-title">
    <header
      class="sticky top-0 z-10 flex h-12 shrink-0 items-center justify-between bg-white"
      role="banner"
    >
      <h1 class="text-xl font-semibold text-gray-900">应用管理</h1>
      <button
        type="button"
        class="block rounded-md bg-blue-600 px-3 py-2 text-center text-sm font-semibold text-white shadow-sm hover:bg-blue-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
        @click="handleClickCreate"
        aria-label="创建新应用"
      >
        创建APP
      </button>
    </header>

    <section ref="ak_table" class="py-4" aria-labelledby="apps-table-title">
      <h2 id="apps-table-title" class="sr-only">应用列表</h2>
      <a-table
        :data="apps"
        :bordered="false"
        :pagination="true"
        page-position="bottom"
        :scroll="scroll"
        :scroll-x="true"
        role="table"
        aria-label="应用管理表格"
      >
        <template #columns>
          <a-table-column
            :ellipsis="true"
            :width="100"
            title="应用ID"
            data-index="_id"
            :tooltip="true"
          ></a-table-column>
          <a-table-column
            :ellipsis="true"
            :width="80"
            title="应用名称"
            data-index="app_name"
            :tooltip="true"
          ></a-table-column>
          <a-table-column
            :width="100"
            title="包名"
            data-index="package_name"
          ></a-table-column>
          <a-table-column
            :ellipsis="true"
            :width="50"
            title="平台"
            data-index="platform"
            :tooltip="true"
          ></a-table-column>
          <a-table-column :width="100" title="操作">
            <template #cell="{ record }">
              <nav role="toolbar" aria-label="应用操作">
                <a-space flex-auto>
                  <a-button
                    status="danger"
                    @click="handleClickDelete(record)"
                    :aria-label="`删除应用 ${record.app_name}`"
                  >
                    删除
                  </a-button>
                </a-space>
              </nav>
            </template>
          </a-table-column>
        </template>
      </a-table>
    </section>

    <!-- 删除确认对话框 -->
    <DeleteConfirmDialog
      v-model:visible="configDeleteVisible"
      :title="'删除应用确认'"
      :content="configDeleteWarning"
      @confirm="handleDeleteConfirm"
      @cancel="handleDeleteCancel"
    />

    <!-- 创建应用对话框 -->
    <aside role="dialog" aria-labelledby="create-dialog-title">
      <a-modal
        v-model:visible="createAppVisible"
        @cancel="handleCreateCancel"
        :on-before-ok="handleCreateBeforeOk"
        aria-labelledby="create-dialog-title"
      >
        <template #title>
          <h3 id="create-dialog-title" class="create-modal-title">创建新应用</h3>
        </template>
        <div>
          <a-form
            :model="createAppForm"
            layout="vertical"
            role="form"
            aria-label="创建应用表单"
          >
            <a-form-item field="app_name" label="应用名称" required>
              <a-input
                v-model="createAppForm.app_name"
                placeholder="请输入应用名称"
                aria-required="true"
                aria-describedby="app-name-help"
              />
              <small id="app-name-help" class="sr-only">请输入应用的显示名称</small>
            </a-form-item>
            <a-form-item field="package_name" label="包名" required>
              <a-input
                v-model="createAppForm.package_name"
                placeholder="请输入包名"
                aria-required="true"
                aria-describedby="package-name-help"
              />
              <small id="package-name-help" class="sr-only"
                >请输入应用的包名，如：com.example.app</small
              >
            </a-form-item>
            <a-form-item field="platform" label="平台" required>
              <a-input
                v-model="createAppForm.platform"
                placeholder="请输入平台，如：android 或 ios"
                aria-required="true"
                aria-describedby="platform-help"
              />
              <small id="platform-help" class="sr-only"
                >请输入应用平台：android 或 ios</small
              >
            </a-form-item>
          </a-form>
        </div>
      </a-modal>
    </aside>
  </article>
</template>

<script setup lang="ts">
import { useAppsStore } from '@/stores/appsStore'
import { storeToRefs } from 'pinia'
import { appsApi } from '@/networking/api'
import { onMounted, ref, watch } from 'vue'
import { tryOnMounted, useWindowSize } from '@vueuse/core'
import DeleteConfirmDialog from '@/components/DeleteConfirmDialog.vue'

const store = useAppsStore()
const { activeApp, apps } = storeToRefs(store)

const { width: windowWidth, height: windowHeight } = useWindowSize()
const ak_table = ref(null as any)
const scroll = ref({
  y: 500
})

watch(windowHeight, () => {
  scroll.value.y = Math.max(windowHeight.value - 165, 300)
})

onMounted(async () => {
  scroll.value.y = Math.max(windowHeight.value - 165, 300)
  try {
    await store.reloadApps()
  } catch (e) {
    console.log(e)
  }
})

watch(
  () => activeApp.value,
  (val) => {
    console.log('activeApp', val)
    store.setActiveApp(val)
  }
)

const configDeleteVisible = ref(false)
const configDeleteWarning = ref('')
const configDeleteForm = ref({
  id: ''
})

function handleClickDelete(record: any) {
  configDeleteVisible.value = true
  configDeleteForm.value.id = record._id
  configDeleteWarning.value = `确定删除 ${record.app_name} 的配置吗?`
}

function handleDeleteCancel() {
  store.reloadApps()
}

async function handleDeleteConfirm() {
  try {
    const res = await appsApi.deleteApp(configDeleteForm.value.id)
    store.reloadApps()
  } catch (e) {
    console.log(e)
  }
}

const createAppVisible = ref(false)
const createAppForm = ref({
  app_name: '',
  package_name: '',
  platform: ''
})

function handleClickCreate() {
  createAppVisible.value = true
  createAppForm.value = {
    app_name: '',
    package_name: '',
    platform: ''
  }
}

function handleCreateCancel() {
  createAppVisible.value = false
}

async function handleCreateBeforeOk(done: (success: boolean) => boolean | void) {
  try {
    const res = await appsApi.createApp(createAppForm.value)
    store.reloadApps()
    done(true)
  } catch (e) {
    done(false)
  }
}
</script>
<style scoped lang="less">
@import './Apps.less';
</style>
