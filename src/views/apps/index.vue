<template>
  <article role="main" aria-labelledby="page-title">
    <header
      :class="[
        'sticky top-0 z-10 flex h-12 shrink-0 items-center justify-between transition-colors duration-300',
        isDark ? 'bg-dark-primary' : 'bg-white'
      ]"
      role="banner"
    >
      <h1
        :class="[
          'text-xl font-semibold transition-colors duration-300',
          isDark ? 'text-white' : 'text-gray-900'
        ]"
      >
        应用管理
      </h1>
      <button
        type="button"
        :class="[
          'block rounded-md px-2 py-1.5 text-center text-sm font-medium transition-all duration-200 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 flex items-center gap-x-2',
          isDark
            ? 'border border-blue-500 text-blue-500 hover:bg-blue-500/10 focus-visible:outline-blue-500'
            : 'border border-blue-600 text-blue-600 hover:bg-blue-600/10 focus-visible:outline-blue-600'
        ]"
        @click="handleClickCreate"
        aria-label="创建新应用"
      >
        <Plus
          class="h-4 w-4"
          :class="[isDark ? 'text-blue-500' : 'text-blue-600']"
        />
        添加APP
      </button>
    </header>

    <section ref="ak_table" class="py-4" aria-labelledby="apps-table-title">
      <h2 id="apps-table-title" class="sr-only">应用列表</h2>

      <!-- 加载时显示 Shimmer 效果 -->
      <ShimmerLoading
        v-if="isAppsLoading"
        type="table"
        :rows="5"
        :columns="5"
        :column-widths="['150px', '120px', '180px', '80px', '120px']"
        container-class="mb-4"
      />

      <!-- 数据表格 -->
      <a-table
        v-else
        :data="apps"
        :bordered="false"
        :pagination="true"
        page-position="bottom"
        :scroll="scroll"
        :scroll-x="true"
        role="table"
        aria-label="应用管理表格"
      >
        <template #columns>
          <a-table-column
            :ellipsis="true"
            :width="100"
            title="应用ID"
            data-index="_id"
            :tooltip="true"
          ></a-table-column>
          <a-table-column
            :ellipsis="true"
            :width="80"
            title="应用名称"
            data-index="app_name"
            :tooltip="true"
          ></a-table-column>
          <a-table-column
            :width="100"
            title="包名"
            data-index="package_name"
          ></a-table-column>
          <a-table-column
            :ellipsis="true"
            :width="50"
            title="平台"
            data-index="platform"
            :tooltip="true"
          ></a-table-column>
          <a-table-column :width="100" title="操作">
            <template #cell="{ record }">
              <nav role="toolbar" aria-label="应用操作">
                <a-space flex-auto>
                  <a-button
                    status="danger"
                    size="mini"
                    :class="[
                      'transition-colors duration-200',
                      isDark ? 'bg-red-900/60 text-red-100 hover:bg-red-800/70' : ''
                    ]"
                    @click="handleClickDelete(record)"
                    :aria-label="`删除应用 ${record.app_name}`"
                  >
                    删除
                  </a-button>
                </a-space>
              </nav>
            </template>
          </a-table-column>
        </template>
      </a-table>
    </section>

    <!-- 删除确认对话框 -->
    <DeleteConfirmDialog
      v-model:visible="configDeleteVisible"
      :title="'删除应用确认'"
      :content="configDeleteWarning"
      :loading="isDeleteLoading"
      @confirm="handleDeleteConfirm"
      @cancel="handleDeleteCancel"
    />

    <!-- 创建应用对话框 -->
    <aside role="dialog" aria-labelledby="create-dialog-title">
      <a-modal
        v-model:visible="createAppVisible"
        @cancel="handleCreateCancel"
        :on-before-ok="handleCreateBeforeOk"
        aria-labelledby="create-dialog-title"
      >
        <template #title>
          <h3 id="create-dialog-title" class="create-modal-title">创建新应用</h3>
        </template>
        <div>
          <a-form
            :model="createAppForm"
            layout="vertical"
            role="form"
            aria-label="创建应用表单"
          >
            <a-form-item field="app_name" label="应用名称" required>
              <a-input
                v-model="createAppForm.app_name"
                placeholder="请输入应用名称"
                aria-required="true"
                aria-describedby="app-name-help"
              />
              <small id="app-name-help" class="sr-only">请输入应用的显示名称</small>
            </a-form-item>
            <a-form-item field="package_name" label="包名" required>
              <a-input
                v-model="createAppForm.package_name"
                placeholder="请输入包名"
                aria-required="true"
                aria-describedby="package-name-help"
              />
              <small id="package-name-help" class="sr-only"
                >请输入应用的包名，如：com.example.app</small
              >
            </a-form-item>
            <a-form-item field="platform" label="平台" required>
              <a-input
                v-model="createAppForm.platform"
                placeholder="请输入平台，如：android 或 ios"
                aria-required="true"
                aria-describedby="platform-help"
              />
              <small id="platform-help" class="sr-only"
                >请输入应用平台：android 或 ios</small
              >
            </a-form-item>
          </a-form>
        </div>
      </a-modal>
    </aside>
  </article>
</template>

<script setup lang="ts">
import { useAppsStore } from '@/stores/appsStore'
import { storeToRefs } from 'pinia'
import { appsApi } from '@/networking/api'
import { onMounted, ref, watch } from 'vue'
import { tryOnMounted, useWindowSize } from '@vueuse/core'
import DeleteConfirmDialog from '@/components/DeleteConfirmDialog.vue'
import ShimmerLoading from '@/components/ShimmerLoading.vue'
import { useThemeStore } from '@/stores/themeStore'
import { useLoading } from '@/composables/useLoading'
import { Plus } from 'lucide-vue-next'
import { validateCreateAppForm } from '@/utils/validation'
import { retryOperation } from '@/utils/errorHandler'
import { useI18n } from 'vue-i18n'
import { Message } from '@arco-design/web-vue'
import type { CreateAppRequest } from '@/types/api'

// 使用国际化
const { t } = useI18n()

const store = useAppsStore()
const { activeApp, apps } = storeToRefs(store)

// 主题管理
const themeStore = useThemeStore()
const { isDark } = storeToRefs(themeStore)

// 加载状态管理
const { isLoading, withLoading } = useLoading(['apps', 'delete', 'create'])
const isAppsLoading = isLoading('apps')
const isDeleteLoading = isLoading('delete')
const isCreateLoading = isLoading('create')

const { width: windowWidth, height: windowHeight } = useWindowSize()
const ak_table = ref(null as any)
const scroll = ref({
  y: 500
})

watch(windowHeight, () => {
  scroll.value.y = Math.max(windowHeight.value - 165, 300)
})

onMounted(async () => {
  scroll.value.y = Math.max(windowHeight.value - 165, 300)
  await withLoading(
    'apps',
    async () => {
      await store.reloadApps()
    },
    {
      onError: (error) => {
        console.error('加载应用列表失败:', error)
        Message.error(t('app.loadError'))
      }
    }
  )
})

watch(
  () => activeApp.value,
  (val) => {
    console.log('activeApp', val)
    store.setActiveApp(val)
  }
)

const configDeleteVisible = ref(false)
const configDeleteWarning = ref('')
const configDeleteForm = ref({
  id: ''
})

function handleClickDelete(record: any) {
  configDeleteVisible.value = true
  configDeleteForm.value.id = record._id
  configDeleteWarning.value = t('app.deleteConfirm')
}

function handleDeleteCancel() {
  store.reloadApps()
}

async function handleDeleteConfirm() {
  await withLoading(
    'delete',
    async () => {
      await retryOperation(async () => {
        const res = await appsApi.deleteApp({ app_id: configDeleteForm.value.id })
        return res
      })
      await store.reloadApps()
      Message.success(t('app.deleteSuccess'))
    },
    {
      onError: (error) => {
        console.error('删除应用失败:', error)
        Message.error(t('app.deleteError'))
      }
    }
  )
}

const createAppVisible = ref(false)
const createAppForm = ref({
  app_name: '',
  package_name: '',
  platform: ''
})

function handleClickCreate() {
  createAppVisible.value = true
  createAppForm.value = {
    app_name: '',
    package_name: '',
    platform: ''
  }
}

function handleCreateCancel() {
  createAppVisible.value = false
}

async function handleCreateBeforeOk(done: (success: boolean) => boolean | void) {
  // 表单验证
  const validation = validateCreateAppForm(createAppForm.value)
  if (!validation.isValid) {
    done(false)
    return
  }

  await withLoading(
    'create',
    async () => {
      // 创建应用
      await retryOperation(async () => {
        const res = await appsApi.createApp(createAppForm.value as CreateAppRequest)
        return res
      })

      await store.reloadApps()
      Message.success(t('app.createSuccess'))
      done(true)
    },
    {
      onError: (error) => {
        console.error('创建应用失败:', error)
        Message.error(t('app.createError'))
        done(false)
      }
    }
  )
}
</script>
<style scoped lang="less">
@import './index.less';
</style>
