<template>
  <div :class="['shimmer-container', containerClass]">
    <!-- 表格骨架屏 -->
    <div v-if="type === 'table'" class="shimmer-table">
      <div v-for="row in rows" :key="`row-${row}`" class="shimmer-table-row">
        <div
          v-for="col in columns"
          :key="`col-${col}`"
          :class="['shimmer-table-cell', isDark ? 'shimmer-dark' : 'shimmer-light']"
          :style="{ width: columnWidths[col - 1] || 'auto' }"
        ></div>
      </div>
    </div>

    <!-- 卡片骨架屏 -->
    <div v-else-if="type === 'card'" class="shimmer-cards">
      <div
        v-for="card in cards"
        :key="`card-${card}`"
        :class="[
          'shimmer-card',
          isDark ? 'shimmer-card-dark' : 'shimmer-card-light'
        ]"
      >
        <div
          :class="['shimmer-card-header', isDark ? 'shimmer-dark' : 'shimmer-light']"
        ></div>
        <div class="shimmer-card-content">
          <div
            v-for="line in 3"
            :key="`line-${line}`"
            :class="['shimmer-card-line', isDark ? 'shimmer-dark' : 'shimmer-light']"
            :style="{ width: line === 3 ? '60%' : '100%' }"
          ></div>
        </div>
      </div>
    </div>

    <!-- 列表骨架屏 -->
    <div v-else-if="type === 'list'" class="shimmer-list">
      <div v-for="item in items" :key="`item-${item}`" class="shimmer-list-item">
        <div
          :class="['shimmer-list-avatar', isDark ? 'shimmer-dark' : 'shimmer-light']"
        ></div>
        <div class="shimmer-list-content">
          <div
            :class="[
              'shimmer-list-title',
              isDark ? 'shimmer-dark' : 'shimmer-light'
            ]"
          ></div>
          <div
            :class="[
              'shimmer-list-subtitle',
              isDark ? 'shimmer-dark' : 'shimmer-light'
            ]"
          ></div>
        </div>
      </div>
    </div>

    <!-- 文本骨架屏 -->
    <div v-else-if="type === 'text'" class="shimmer-text">
      <div
        v-for="line in textLines"
        :key="`text-${line}`"
        :class="['shimmer-text-line', isDark ? 'shimmer-dark' : 'shimmer-light']"
        :style="{ width: line === textLines ? '70%' : '100%' }"
      ></div>
    </div>

    <!-- 自定义骨架屏 -->
    <div v-else class="shimmer-custom">
      <slot>
        <div
          :class="['shimmer-default', isDark ? 'shimmer-dark' : 'shimmer-light']"
        ></div>
      </slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useThemeStore } from '@/stores/themeStore'
import { storeToRefs } from 'pinia'

interface Props {
  type?: 'table' | 'card' | 'list' | 'text' | 'custom'
  rows?: number
  columns?: number
  cards?: number
  items?: number
  textLines?: number
  columnWidths?: string[]
  containerClass?: string
}

const props = withDefaults(defineProps<Props>(), {
  type: 'custom',
  rows: 5,
  columns: 4,
  cards: 3,
  items: 6,
  textLines: 4,
  columnWidths: () => [],
  containerClass: ''
})

const themeStore = useThemeStore()
const { isDark } = storeToRefs(themeStore)
</script>

<style scoped>
.shimmer-container {
  @apply w-full;
}

/* 基础 shimmer 动画 */
.shimmer-light,
.shimmer-dark {
  @apply relative overflow-hidden;
  animation: shimmer 2s infinite linear;
}

.shimmer-light {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
}

.shimmer-dark {
  background: linear-gradient(90deg, #2d3748 25%, #4a5568 50%, #2d3748 75%);
  background-size: 200% 100%;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* 表格骨架屏 */
.shimmer-table {
  @apply w-full space-y-2;
}

.shimmer-table-row {
  @apply flex space-x-4;
}

.shimmer-table-cell {
  @apply h-12 rounded-md flex-1;
}

/* 卡片骨架屏 */
.shimmer-cards {
  @apply grid gap-6;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
}

.shimmer-card {
  @apply rounded-lg p-6 shadow-sm;
}

.shimmer-card-light {
  @apply bg-white border border-gray-200;
}

.shimmer-card-dark {
  @apply bg-dark-secondary border border-gray-600;
}

.shimmer-card-header {
  @apply h-6 rounded-md mb-4;
}

.shimmer-card-content {
  @apply space-y-3;
}

.shimmer-card-line {
  @apply h-4 rounded-md;
}

/* 列表骨架屏 */
.shimmer-list {
  @apply space-y-4;
}

.shimmer-list-item {
  @apply flex items-center space-x-4;
}

.shimmer-list-avatar {
  @apply w-12 h-12 rounded-full flex-shrink-0;
}

.shimmer-list-content {
  @apply flex-1 space-y-2;
}

.shimmer-list-title {
  @apply h-4 rounded-md w-3/4;
}

.shimmer-list-subtitle {
  @apply h-3 rounded-md w-1/2;
}

/* 文本骨架屏 */
.shimmer-text {
  @apply space-y-3;
}

.shimmer-text-line {
  @apply h-4 rounded-md;
}

/* 默认骨架屏 */
.shimmer-default {
  @apply h-32 rounded-lg;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .shimmer-cards {
    grid-template-columns: 1fr;
  }

  .shimmer-table-cell {
    @apply min-w-20;
  }
}
</style>
