<template>
  <Teleport to="body">
    <Transition
      enter-active-class="ease-out duration-300"
      enter-from-class="opacity-0"
      enter-to-class="opacity-100"
      leave-active-class="ease-in duration-200"
      leave-from-class="opacity-100"
      leave-to-class="opacity-0"
    >
      <div v-if="visible" class="relative z-50">
        <div
          :class="[
            'fixed inset-0 transition-opacity',
            isDark
              ? 'bg-gray-900 bg-opacity-80'
              : 'bg-gray-500 bg-opacity-75'
          ]"
        ></div>

        <div class="fixed inset-0 z-50 w-screen overflow-y-auto">
          <div
            class="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0"
          >
            <Transition
              enter-active-class="ease-out duration-300"
              enter-from-class="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              enter-to-class="opacity-100 translate-y-0 sm:scale-100"
              leave-active-class="ease-in duration-200"
              leave-from-class="opacity-100 translate-y-0 sm:scale-100"
              leave-to-class="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            >
              <div
                v-if="visible"
                :class="[
                  'relative transform overflow-hidden rounded-lg text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg',
                  isDark ? 'bg-dark-secondary' : 'bg-white'
                ]"
              >
                <div :class="[
                  'px-4 pb-4 pt-5 sm:p-6 sm:pb-4',
                  isDark ? 'bg-dark-secondary' : 'bg-white'
                ]">
                  <div class="sm:flex sm:items-start">
                    <div
                      :class="[
                        'mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full sm:mx-0 sm:h-10 sm:w-10',
                        isDark ? 'bg-red-900/30' : 'bg-red-100'
                      ]"
                    >
                      <svg
                        class="h-6 w-6 text-red-600"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke-width="1.5"
                        stroke="currentColor"
                        aria-hidden="true"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z"
                        />
                      </svg>
                    </div>
                    <div class="mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left">
                      <h3
                        :class="[
                          'text-base font-semibold leading-6',
                          isDark ? 'text-white' : 'text-gray-900'
                        ]"
                        id="modal-title"
                      >
                        {{ title }}
                      </h3>
                      <div class="mt-2">
                        <p
                          v-if="typeof content === 'string'"
                          :class="[
                            'text-sm',
                            isDark ? 'text-gray-300' : 'text-gray-500'
                          ]"
                        >
                          {{ content }}
                        </p>
                        <div
                          v-else-if="content"
                          v-html="content"
                          :class="[
                            'text-sm',
                            isDark ? 'text-gray-300' : 'text-gray-500'
                          ]"
                        ></div>
                        <p v-else :class="[
                          'text-sm',
                          isDark ? 'text-gray-300' : 'text-gray-500'
                        ]">
                          此操作不可撤销，请确认是否继续？
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  :class="[
                    'px-4 py-3 sm:flex sm:flex-row-reverse sm:px-6',
                    isDark ? 'bg-dark-tertiary' : 'bg-gray-50'
                  ]"
                >
                  <button
                    type="button"
                    class="inline-flex w-full justify-center rounded-md bg-red-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-red-500 sm:ml-3 sm:w-auto"
                    :class="{ 'opacity-50 cursor-not-allowed': loading }"
                    :disabled="loading"
                    @click="handleConfirm"
                  >
                    <svg
                      v-if="loading"
                      class="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        class="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        stroke-width="4"
                      ></circle>
                      <path
                        class="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      ></path>
                    </svg>
                    {{ confirmText }}
                  </button>
                  <button
                    type="button"
                    :class="[
                      'mt-3 inline-flex w-full justify-center rounded-md px-3 py-2 text-sm font-semibold shadow-sm ring-1 ring-inset sm:mt-0 sm:w-auto transition-colors duration-200',
                      isDark
                        ? 'bg-dark-secondary text-white ring-gray-600 hover:bg-dark-primary'
                        : 'bg-white text-gray-900 ring-gray-300 hover:bg-gray-50'
                    ]"
                    @click="handleCancel"
                  >
                    {{ cancelText }}
                  </button>
                </div>
              </div>
            </Transition>
          </div>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useThemeStore } from '@/stores/themeStore'
import { storeToRefs } from 'pinia'

interface Props {
  title?: string
  content?: string
  confirmText?: string
  cancelText?: string
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  title: '确认删除',
  content: '',
  confirmText: '确认删除',
  cancelText: '取消',
  loading: false
})

const emit = defineEmits<{
  confirm: []
  cancel: []
}>()

const visible = defineModel<boolean>('visible', { default: false })

// 主题管理
const themeStore = useThemeStore()
const { isDark } = storeToRefs(themeStore)

const handleConfirm = async () => {
  try {
    emit('confirm')
  } catch (error) {
    console.error(error)
  }
}

const handleCancel = () => {
  emit('cancel')
  visible.value = false
}
</script>
