<template>
  <div class="theme-toggle">
    <!-- 简单切换按钮 -->
    <button
      v-if="!showDropdown"
      @click="handleToggle"
      :class="[
        'group flex items-center gap-x-3 rounded-md p-3 text-sm font-semibold leading-6 transition-all duration-200 ease-out transform hover:scale-105',
        isDark
          ? 'text-gray-300 hover:text-white hover:bg-gray-800'
          : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
      ]"
      :title="getThemeDisplayName"
      :aria-label="`切换到${isDark ? '浅色' : '深色'}模式`"
    >
      <component
        :is="getThemeIcon"
        :class="[
          'h-6 w-6 shrink-0 transition-colors duration-200',
          isDark ? 'text-gray-300 group-hover:text-white' : 'text-gray-600 group-hover:text-gray-900'
        ]"
        aria-hidden="true"
      />
      <span class="truncate">{{ getThemeDisplayName }}</span>
    </button>

    <!-- 下拉菜单版本 -->
    <Menu v-else as="div" class="relative">
      <MenuButton
        :class="[
          'group flex w-full items-center gap-x-3 rounded-md p-3 text-sm font-semibold leading-6 transition-all duration-200 ease-out transform hover:scale-105',
          isDark
            ? 'text-gray-300 hover:text-white hover:bg-gray-800'
            : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
        ]"
      >
        <component
          :is="getThemeIcon"
          :class="[
            'h-6 w-6 shrink-0 transition-colors duration-200',
            isDark ? 'text-gray-300 group-hover:text-white' : 'text-gray-600 group-hover:text-gray-900'
          ]"
          aria-hidden="true"
        />
        <span class="truncate">{{ getThemeDisplayName }}</span>
        <ChevronDown
          :class="[
            'ml-auto h-5 w-5 shrink-0 transition-transform ui-open:rotate-180',
            isDark ? 'text-gray-400 group-hover:text-white' : 'text-gray-500 group-hover:text-gray-900'
          ]"
          aria-hidden="true"
        />
      </MenuButton>
      
      <transition
        enter-active-class="transition ease-out duration-100"
        enter-from-class="transform opacity-0 scale-95"
        enter-to-class="transform opacity-100 scale-100"
        leave-active-class="transition ease-in duration-75"
        leave-from-class="transform opacity-100 scale-100"
        leave-to-class="transform opacity-0 scale-95"
      >
        <MenuItems
          :class="[
            'absolute bottom-full left-0 z-10 mb-2 w-48 origin-bottom-left rounded-md shadow-lg ring-1 ring-opacity-5 focus:outline-none',
            isDark
              ? 'bg-gray-800 ring-white/5'
              : 'bg-white ring-black/5'
          ]"
        >
          <div class="py-1">
            <MenuItem
              v-for="option in themeOptions"
              :key="option.value"
              v-slot="{ active }"
            >
              <button
                @click="setTheme(option.value)"
                :class="[
                  active
                    ? isDark
                      ? 'bg-gray-700 text-white'
                      : 'bg-gray-100 text-gray-900'
                    : isDark
                      ? 'text-gray-300'
                      : 'text-gray-700',
                  'group flex w-full items-center rounded-md px-4 py-2 text-sm',
                  mode === option.value && 'font-semibold'
                ]"
              >
                <component
                  :is="option.icon"
                  :class="[
                    'mr-3 h-5 w-5',
                    active
                      ? isDark
                        ? 'text-white'
                        : 'text-gray-900'
                      : isDark
                        ? 'text-gray-400'
                        : 'text-gray-500'
                  ]"
                  aria-hidden="true"
                />
                {{ option.label }}
                <Check
                  v-if="mode === option.value"
                  class="ml-auto h-4 w-4"
                  aria-hidden="true"
                />
              </button>
            </MenuItem>
          </div>
        </MenuItems>
      </transition>
    </Menu>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Menu, MenuButton, MenuItem, MenuItems } from '@headlessui/vue'
import { Sun, Moon, Monitor, ChevronDown, Check } from 'lucide-vue-next'
import { useThemeStore, type ThemeMode } from '@/stores/themeStore'

interface Props {
  showDropdown?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showDropdown: false
})

const themeStore = useThemeStore()
const { mode, isDark, setTheme, toggleDark, getThemeDisplayName } = themeStore

// 主题选项
const themeOptions = [
  { value: 'light' as ThemeMode, label: '浅色模式', icon: Sun },
  { value: 'dark' as ThemeMode, label: '深色模式', icon: Moon },
  { value: 'system' as ThemeMode, label: '跟随系统', icon: Monitor }
]

// 获取当前主题图标
const getThemeIcon = computed(() => {
  if (mode === 'system') {
    return isDark ? Moon : Sun
  }
  return mode === 'dark' ? Moon : Sun
})

// 处理简单切换
const handleToggle = () => {
  toggleDark()
}
</script>

<style scoped>
.theme-toggle {
  @apply w-full;
}

/* 按钮点击反馈效果 */
.theme-toggle button {
  position: relative;
  overflow: hidden;
}

.theme-toggle button::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.theme-toggle button:active::after {
  width: 300px;
  height: 300px;
}
</style>
