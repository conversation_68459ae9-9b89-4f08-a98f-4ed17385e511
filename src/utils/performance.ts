/**
 * 性能监控和优化工具
 */

// 性能指标接口
export interface PerformanceMetrics {
  name: string
  startTime: number
  endTime: number
  duration: number
  type: 'navigation' | 'api' | 'component' | 'custom'
  metadata?: Record<string, any>
}

// 性能监控类
export class PerformanceMonitor {
  private static instance: PerformanceMonitor
  private metrics: PerformanceMetrics[] = []
  private timers: Map<string, number> = new Map()
  private observers: PerformanceObserver[] = []

  private constructor() {
    this.initObservers()
  }

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor()
    }
    return PerformanceMonitor.instance
  }

  /**
   * 初始化性能观察器
   */
  private initObservers(): void {
    // 观察导航时间
    if ('PerformanceObserver' in window) {
      try {
        // 观察页面加载性能
        const navObserver = new PerformanceObserver((list) => {
          list.getEntries().forEach((entry) => {
            if (entry.entryType === 'navigation') {
              this.recordNavigationMetrics(entry as PerformanceNavigationTiming)
            }
          })
        })
        navObserver.observe({ entryTypes: ['navigation'] })
        this.observers.push(navObserver)

        // 观察资源加载性能
        const resourceObserver = new PerformanceObserver((list) => {
          list.getEntries().forEach((entry) => {
            if (entry.entryType === 'resource') {
              this.recordResourceMetrics(entry as PerformanceResourceTiming)
            }
          })
        })
        resourceObserver.observe({ entryTypes: ['resource'] })
        this.observers.push(resourceObserver)

        // 观察 LCP (Largest Contentful Paint)
        const lcpObserver = new PerformanceObserver((list) => {
          list.getEntries().forEach((entry) => {
            this.recordMetric({
              name: 'LCP',
              startTime: entry.startTime,
              endTime: entry.startTime,
              duration: entry.startTime,
              type: 'navigation',
              metadata: {
                element: (entry as any).element,
                url: (entry as any).url
              }
            })
          })
        })
        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] })
        this.observers.push(lcpObserver)

        // 观察 FID (First Input Delay)
        const fidObserver = new PerformanceObserver((list) => {
          list.getEntries().forEach((entry) => {
            this.recordMetric({
              name: 'FID',
              startTime: entry.startTime,
              endTime: entry.startTime + entry.duration,
              duration: entry.duration,
              type: 'navigation'
            })
          })
        })
        fidObserver.observe({ entryTypes: ['first-input'] })
        this.observers.push(fidObserver)
      } catch (e) {
        console.warn('Failed to initialize performance observers:', e)
      }
    }
  }

  /**
   * 记录导航性能指标
   */
  private recordNavigationMetrics(entry: PerformanceNavigationTiming): void {
    const metrics = [
      {
        name: 'DNS查询时间',
        duration: entry.domainLookupEnd - entry.domainLookupStart
      },
      {
        name: 'TCP连接时间',
        duration: entry.connectEnd - entry.connectStart
      },
      {
        name: '请求响应时间',
        duration: entry.responseEnd - entry.requestStart
      },
      {
        name: 'DOM解析时间',
        duration: entry.domContentLoadedEventEnd - entry.domContentLoadedEventStart
      },
      {
        name: '页面加载完成时间',
        duration: entry.loadEventEnd - entry.loadEventStart
      },
      {
        name: '首次渲染时间',
        duration: entry.domContentLoadedEventEnd - entry.navigationStart
      }
    ]

    metrics.forEach(metric => {
      this.recordMetric({
        name: metric.name,
        startTime: entry.navigationStart,
        endTime: entry.navigationStart + metric.duration,
        duration: metric.duration,
        type: 'navigation'
      })
    })
  }

  /**
   * 记录资源加载性能指标
   */
  private recordResourceMetrics(entry: PerformanceResourceTiming): void {
    // 只记录关键资源
    if (this.isImportantResource(entry.name)) {
      this.recordMetric({
        name: `资源加载: ${this.getResourceName(entry.name)}`,
        startTime: entry.startTime,
        endTime: entry.responseEnd,
        duration: entry.responseEnd - entry.startTime,
        type: 'navigation',
        metadata: {
          url: entry.name,
          size: entry.transferSize,
          cached: entry.transferSize === 0
        }
      })
    }
  }

  /**
   * 判断是否为重要资源
   */
  private isImportantResource(url: string): boolean {
    return url.includes('.js') || url.includes('.css') || url.includes('.vue')
  }

  /**
   * 获取资源名称
   */
  private getResourceName(url: string): string {
    return url.split('/').pop() || url
  }

  /**
   * 开始计时
   */
  startTimer(name: string): void {
    this.timers.set(name, performance.now())
  }

  /**
   * 结束计时并记录
   */
  endTimer(name: string, type: PerformanceMetrics['type'] = 'custom', metadata?: Record<string, any>): void {
    const startTime = this.timers.get(name)
    if (startTime !== undefined) {
      const endTime = performance.now()
      this.recordMetric({
        name,
        startTime,
        endTime,
        duration: endTime - startTime,
        type,
        metadata
      })
      this.timers.delete(name)
    }
  }

  /**
   * 记录自定义指标
   */
  recordMetric(metric: PerformanceMetrics): void {
    this.metrics.push(metric)
    
    // 保持指标数量在合理范围内
    if (this.metrics.length > 1000) {
      this.metrics = this.metrics.slice(-500)
    }

    // 在开发环境下打印慢操作
    if (import.meta.env.DEV && metric.duration > 100) {
      console.warn(`⚠️ 慢操作检测: ${metric.name} 耗时 ${metric.duration.toFixed(2)}ms`)
    }
  }

  /**
   * 记录 API 请求性能
   */
  recordApiPerformance(url: string, method: string, duration: number, success: boolean): void {
    this.recordMetric({
      name: `API: ${method} ${url}`,
      startTime: performance.now() - duration,
      endTime: performance.now(),
      duration,
      type: 'api',
      metadata: {
        url,
        method,
        success
      }
    })
  }

  /**
   * 记录组件渲染性能
   */
  recordComponentPerformance(componentName: string, duration: number): void {
    this.recordMetric({
      name: `组件渲染: ${componentName}`,
      startTime: performance.now() - duration,
      endTime: performance.now(),
      duration,
      type: 'component'
    })
  }

  /**
   * 获取性能指标
   */
  getMetrics(type?: PerformanceMetrics['type']): PerformanceMetrics[] {
    return type ? this.metrics.filter(m => m.type === type) : [...this.metrics]
  }

  /**
   * 获取性能报告
   */
  getPerformanceReport(): {
    summary: {
      totalMetrics: number
      averageDuration: number
      slowestOperation: PerformanceMetrics | null
      fastestOperation: PerformanceMetrics | null
    }
    byType: Record<string, {
      count: number
      averageDuration: number
      totalDuration: number
    }>
  } {
    const metrics = this.metrics
    
    const summary = {
      totalMetrics: metrics.length,
      averageDuration: metrics.length > 0 ? metrics.reduce((sum, m) => sum + m.duration, 0) / metrics.length : 0,
      slowestOperation: metrics.length > 0 ? metrics.reduce((max, current) => current.duration > max.duration ? current : max) : null,
      fastestOperation: metrics.length > 0 ? metrics.reduce((min, current) => current.duration < min.duration ? current : min) : null
    }

    const byType: Record<string, { count: number; averageDuration: number; totalDuration: number }> = {}
    
    metrics.forEach(metric => {
      if (!byType[metric.type]) {
        byType[metric.type] = { count: 0, averageDuration: 0, totalDuration: 0 }
      }
      byType[metric.type].count++
      byType[metric.type].totalDuration += metric.duration
    })

    Object.keys(byType).forEach(type => {
      byType[type].averageDuration = byType[type].totalDuration / byType[type].count
    })

    return { summary, byType }
  }

  /**
   * 清除指标
   */
  clearMetrics(): void {
    this.metrics = []
  }

  /**
   * 销毁观察器
   */
  destroy(): void {
    this.observers.forEach(observer => observer.disconnect())
    this.observers = []
    this.timers.clear()
    this.metrics = []
  }
}

// 性能装饰器
export function measurePerformance(name?: string) {
  return function(target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value
    const metricName = name || `${target.constructor.name}.${propertyKey}`

    descriptor.value = async function(...args: any[]) {
      const monitor = PerformanceMonitor.getInstance()
      monitor.startTimer(metricName)
      
      try {
        const result = await originalMethod.apply(this, args)
        monitor.endTimer(metricName, 'custom', { success: true })
        return result
      } catch (error) {
        monitor.endTimer(metricName, 'custom', { success: false, error: error.message })
        throw error
      }
    }

    return descriptor
  }
}

// Vue 组件性能监控 mixin
export const performanceMixin = {
  beforeCreate() {
    this._performanceStart = performance.now()
  },
  mounted() {
    if (this._performanceStart) {
      const duration = performance.now() - this._performanceStart
      PerformanceMonitor.getInstance().recordComponentPerformance(
        this.$options.name || 'Anonymous',
        duration
      )
    }
  }
}

// 导出单例实例
export const performanceMonitor = PerformanceMonitor.getInstance()

// 便捷函数
export const startTimer = (name: string) => performanceMonitor.startTimer(name)
export const endTimer = (name: string, type?: PerformanceMetrics['type'], metadata?: Record<string, any>) => 
  performanceMonitor.endTimer(name, type, metadata)
export const recordMetric = (metric: PerformanceMetrics) => performanceMonitor.recordMetric(metric)

// 内存使用监控
export function getMemoryUsage(): {
  used: number
  total: number
  percentage: number
} | null {
  if ('memory' in performance) {
    const memory = (performance as any).memory
    return {
      used: memory.usedJSHeapSize,
      total: memory.totalJSHeapSize,
      percentage: (memory.usedJSHeapSize / memory.totalJSHeapSize) * 100
    }
  }
  return null
}

// 页面可见性监控
export function initVisibilityMonitoring(): void {
  let hiddenTime: number | null = null

  document.addEventListener('visibilitychange', () => {
    if (document.hidden) {
      hiddenTime = performance.now()
    } else if (hiddenTime) {
      const duration = performance.now() - hiddenTime
      performanceMonitor.recordMetric({
        name: '页面隐藏时间',
        startTime: hiddenTime,
        endTime: performance.now(),
        duration,
        type: 'navigation'
      })
      hiddenTime = null
    }
  })
}

