import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest'
import { ErrorHandler, ErrorType, handleApiError, handleValidationError } from '../errorHandler'

// Mock Arco Design 组件
vi.mock('@arco-design/web-vue', () => ({
  Message: {
    error: vi.fn(),
    warning: vi.fn(),
    success: vi.fn()
  },
  Notification: {
    error: vi.fn(),
    warning: vi.fn(),
    success: vi.fn()
  }
}))

// Mock 环境变量
vi.mock('import.meta.env', () => ({
  DEV: true,
  PROD: false,
  VITE_OATH_URL: 'http://localhost:3000/login'
}))

describe('ErrorHandler', () => {
  let errorHandler: ErrorHandler
  
  beforeEach(() => {
    // 重置单例
    ;(ErrorHandler as any).instance = null
    errorHandler = ErrorHandler.getInstance()
    
    // Mock console methods
    vi.spyOn(console, 'error').mockImplementation(() => {})
    vi.spyOn(console, 'warn').mockImplementation(() => {})
    vi.spyOn(console, 'group').mockImplementation(() => {})
    vi.spyOn(console, 'groupEnd').mockImplementation(() => {})
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('singleton pattern', () => {
    it('should return the same instance', () => {
      const instance1 = ErrorHandler.getInstance()
      const instance2 = ErrorHandler.getInstance()
      expect(instance1).toBe(instance2)
    })
  })

  describe('handleApiError', () => {
    it('should handle API error with response', () => {
      const mockError = {
        response: {
          status: 400,
          data: {
            message: 'Bad Request'
          }
        }
      }

      const appError = errorHandler.handleApiError(mockError)

      expect(appError.type).toBe(ErrorType.API)
      expect(appError.code).toBe(400)
      expect(appError.message).toBe('Bad Request')
    })

    it('should handle network error', () => {
      const mockError = {
        request: {},
        message: 'Network Error'
      }

      const appError = errorHandler.handleApiError(mockError)

      expect(appError.type).toBe(ErrorType.NETWORK)
      expect(appError.code).toBe(0)
    })

    it('should handle unknown error', () => {
      const mockError = {
        message: 'Unknown error'
      }

      const appError = errorHandler.handleApiError(mockError)

      expect(appError.type).toBe(ErrorType.UNKNOWN)
      expect(appError.message).toBe('Unknown error')
    })

    it('should use default error message when no message provided', () => {
      const mockError = {
        response: {
          status: 500,
          data: {}
        }
      }

      const appError = errorHandler.handleApiError(mockError)

      expect(appError.message).toBe('服务器内部错误')
    })
  })

  describe('handleValidationError', () => {
    it('should create validation error correctly', () => {
      const appError = errorHandler.handleValidationError('username', '用户名是必填项')

      expect(appError.type).toBe(ErrorType.VALIDATION)
      expect(appError.code).toBe(422)
      expect(appError.message).toBe('username: 用户名是必填项')
      expect(appError.details).toEqual({
        field: 'username',
        message: '用户名是必填项'
      })
    })
  })

  describe('error queue management', () => {
    it('should limit error queue size', () => {
      // Add more than 100 errors
      for (let i = 0; i < 150; i++) {
        errorHandler.handleApiError({
          message: `Error ${i}`
        })
      }

      const history = errorHandler.getErrorHistory()
      expect(history.length).toBeLessThanOrEqual(100)
    })

    it('should clear error history', () => {
      errorHandler.handleApiError({ message: 'Test error' })
      expect(errorHandler.getErrorHistory().length).toBeGreaterThan(0)
      
      errorHandler.clearErrorHistory()
      expect(errorHandler.getErrorHistory().length).toBe(0)
    })
  })

  describe('retry mechanism', () => {
    it('should retry failed operations', async () => {
      let attemptCount = 0
      const operation = vi.fn().mockImplementation(() => {
        attemptCount++
        if (attemptCount < 3) {
          throw new Error('Operation failed')
        }
        return 'success'
      })

      // Mock setTimeout 以避免真实的延迟
      vi.useFakeTimers()
      
      const retryPromise = errorHandler.retry(operation, 3)
      
      // 快进所有延迟
      await vi.runAllTimersAsync()
      
      const result = await retryPromise
      expect(result).toBe('success')
      expect(operation).toHaveBeenCalledTimes(3)
      
      vi.useRealTimers()
    }, 10000)

    it('should fail after max retries', async () => {
      const operation = vi.fn().mockRejectedValue(new Error('Always fails'))

      await expect(errorHandler.retry(operation, 2)).rejects.toThrow('Always fails')
      expect(operation).toHaveBeenCalledTimes(2)
    })
  })
})

describe('convenience functions', () => {
  beforeEach(() => {
    // 重置单例
    ;(ErrorHandler as any).instance = null
  })

  it('handleApiError should work as expected', () => {
    const mockError = {
      response: {
        status: 404,
        data: { message: 'Not Found' }
      }
    }

    const appError = handleApiError(mockError)
    expect(appError.type).toBe(ErrorType.API)
    expect(appError.code).toBe(404)
  })

  it('handleValidationError should work as expected', () => {
    const appError = handleValidationError('email', '邮箱格式不正确')
    expect(appError.type).toBe(ErrorType.VALIDATION)
    expect(appError.message).toBe('email: 邮箱格式不正确')
  })
})

