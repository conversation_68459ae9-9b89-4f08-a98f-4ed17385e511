import { describe, it, expect, beforeEach, vi } from 'vitest'
import { 
  FormValidator, 
  validateForm, 
  validateCreateAppForm, 
  validateCreateApiKeyForm,
  commonRules 
} from '../validation'

// Mock error handler
vi.mock('../errorHandler', () => ({
  handleValidationError: vi.fn()
}))

describe('FormValidator', () => {
  let validator: FormValidator

  beforeEach(() => {
    validator = new FormValidator()
  })

  describe('basic validation', () => {
    it('should validate required fields', () => {
      validator.addRule('username', [{ required: true }])
      
      const errors = validator.validateField('username', '')
      expect(errors).toHaveLength(1)
      expect(errors[0]).toBe('username是必填项')
    })

    it('should pass validation for valid required field', () => {
      validator.addRule('username', [{ required: true }])
      
      const errors = validator.validateField('username', 'john')
      expect(errors).toHaveLength(0)
    })

    it('should validate minimum length', () => {
      validator.addRule('password', [{ min: 6 }])
      
      const errors = validator.validateField('password', '123')
      expect(errors).toHaveLength(1)
      expect(errors[0]).toBe('password最少需要6个字符')
    })

    it('should validate maximum length', () => {
      validator.addRule('username', [{ max: 10 }])
      
      const errors = validator.validateField('username', 'verylongusername')
      expect(errors).toHaveLength(1)
      expect(errors[0]).toBe('username最多只能有10个字符')
    })

    it('should validate pattern', () => {
      validator.addRule('email', [{ pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/ }])
      
      const errors = validator.validateField('email', 'invalid-email')
      expect(errors).toHaveLength(1)
      expect(errors[0]).toBe('email格式不正确')
    })

    it('should validate custom rule returning string', () => {
      validator.addRule('age', [{
        custom: (value) => {
          const age = parseInt(value)
          if (age < 18) return '年龄必须大于18岁'
          return true
        }
      }])
      
      const errors = validator.validateField('age', '16')
      expect(errors).toHaveLength(1)
      expect(errors[0]).toBe('年龄必须大于18岁')
    })

    it('should validate custom rule returning boolean', () => {
      validator.addRule('confirm', [{
        custom: (value) => value === 'password123',
        message: '密码确认不匹配'
      }])
      
      const errors = validator.validateField('confirm', 'wrongpassword')
      expect(errors).toHaveLength(1)
      expect(errors[0]).toBe('密码确认不匹配')
    })
  })

  describe('multiple rules', () => {
    it('should validate multiple rules and return all errors', () => {
      validator.addRule('password', [
        { required: true },
        { min: 8 },
        { pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/ }
      ])
      
      const errors = validator.validateField('password', '123')
      expect(errors.length).toBeGreaterThan(1)
    })

    it('should skip other validations if not required and empty', () => {
      validator.addRule('optional', [
        { min: 5 },
        { max: 10 }
      ])
      
      const errors = validator.validateField('optional', '')
      expect(errors).toHaveLength(0)
    })
  })

  describe('form validation', () => {
    it('should validate entire form', () => {
      validator
        .addRule('username', [{ required: true }])
        .addRule('email', [{ required: true }, commonRules.email])
      
      const errors = validator.validate({
        username: '',
        email: 'invalid-email'
      })
      
      expect(errors.username).toHaveLength(1)
      expect(errors.email).toHaveLength(1)
    })

    it('should return empty errors for valid form', () => {
      validator
        .addRule('username', [{ required: true }])
        .addRule('email', [{ required: true }, commonRules.email])
      
      const errors = validator.validate({
        username: 'john',
        email: '<EMAIL>'
      })
      
      // 检查是否有任何字段有错误
      const hasErrors = Object.values(errors).some(fieldErrors => fieldErrors.length > 0)
      expect(hasErrors).toBe(false)
    })
  })

  describe('utility methods', () => {
    it('should check if has errors', () => {
      validator.addRule('field', [{ required: true }])
      validator.validateField('field', '')
      
      expect(validator.hasErrors()).toBe(true)
    })

    it('should get first error', () => {
      validator.addRule('field1', [{ required: true }])
      validator.addRule('field2', [{ required: true }])
      
      validator.validate({ field1: '', field2: '' })
      
      const firstError = validator.getFirstError()
      expect(firstError).toBeTruthy()
    })

    it('should clear errors', () => {
      validator.addRule('field', [{ required: true }])
      validator.validateField('field', '')
      
      expect(validator.hasErrors()).toBe(true)
      
      validator.clearErrors()
      expect(validator.hasErrors()).toBe(false)
    })

    it('should clear field error', () => {
      validator.addRule('field1', [{ required: true }])
      validator.addRule('field2', [{ required: true }])
      
      validator.validate({ field1: '', field2: '' })
      
      validator.clearFieldError('field1')
      const errors = validator.getErrors()
      
      expect(errors.field1).toBeUndefined()
      expect(errors.field2).toBeDefined()
    })
  })
})

describe('common rules', () => {
  let validator: FormValidator

  beforeEach(() => {
    validator = new FormValidator()
  })

  it('should validate email format', () => {
    validator.addRule('email', [commonRules.email])
    
    expect(validator.validateField('email', '<EMAIL>')).toHaveLength(0)
    expect(validator.validateField('email', 'invalid-email')).toHaveLength(1)
  })

  it('should validate phone format', () => {
    validator.addRule('phone', [commonRules.phone])
    
    expect(validator.validateField('phone', '13800138000')).toHaveLength(0)
    expect(validator.validateField('phone', '1234567890')).toHaveLength(1)
  })

  it('should validate URL format', () => {
    validator.addRule('url', [commonRules.url])
    
    expect(validator.validateField('url', 'https://example.com')).toHaveLength(0)
    expect(validator.validateField('url', 'not-a-url')).toHaveLength(1)
  })

  it('should validate package name format', () => {
    validator.addRule('package', [commonRules.packageName])
    
    expect(validator.validateField('package', 'com.example.app')).toHaveLength(0)
    expect(validator.validateField('package', 'invalid-package')).toHaveLength(1)
  })

  it('should validate version format', () => {
    validator.addRule('version', [commonRules.version])
    
    expect(validator.validateField('version', '1.0.0')).toHaveLength(0)
    expect(validator.validateField('version', '1.0')).toHaveLength(1)
  })

  it('should validate alphanumeric format', () => {
    validator.addRule('code', [commonRules.alphanumeric])
    
    expect(validator.validateField('code', 'abc123')).toHaveLength(0)
    expect(validator.validateField('code', 'abc-123')).toHaveLength(1)
  })
})

describe('validateForm convenience function', () => {
  it('should return validation result', () => {
    const result = validateForm(
      { username: '', email: 'invalid' },
      {
        username: [commonRules.required],
        email: [commonRules.required, commonRules.email]
      }
    )
    
    expect(result.isValid).toBe(false)
    expect(result.errors.username).toHaveLength(1)
    expect(result.errors.email).toHaveLength(1)
    expect(result.firstError).toBeTruthy()
  })
})

describe('validateCreateAppForm', () => {
  it('should validate app creation form correctly', () => {
    const validData = {
      app_name: 'MyApp',
      package_name: 'com.example.myapp',
      platform: 'android'
    }
    
    const result = validateCreateAppForm(validData)
    expect(result.isValid).toBe(true)
  })

  it('should fail validation for invalid data', () => {
    const invalidData = {
      app_name: '',
      package_name: 'invalid-package',
      platform: 'windows'
    }
    
    const result = validateCreateAppForm(invalidData)
    expect(result.isValid).toBe(false)
    expect(result.errors.app_name).toHaveLength(1)
    expect(result.errors.package_name).toHaveLength(1)
    expect(result.errors.platform).toHaveLength(1)
  })
})

describe('validateCreateApiKeyForm', () => {
  it('should validate API key creation form correctly', () => {
    const validData = {
      name: 'mykey123',
      description: 'Test API key'
    }
    
    const result = validateCreateApiKeyForm(validData)
    expect(result.isValid).toBe(true)
  })

  it('should fail validation for invalid data', () => {
    const invalidData = {
      name: 'x',
      description: 'x'.repeat(201)
    }
    
    const result = validateCreateApiKeyForm(invalidData)
    expect(result.isValid).toBe(false)
    expect(result.errors.name).toHaveLength(1)
    expect(result.errors.description).toHaveLength(1)
  })
})

