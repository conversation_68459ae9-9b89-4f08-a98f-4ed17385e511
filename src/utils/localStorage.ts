/**
 * 安全的 localStorage JSON 操作工具
 */

/**
 * 安全地从 localStorage 读取并解析 JSON 数据
 * @param key - localStorage 键名
 * @param defaultValue - 默认值，如果解析失败时返回
 * @returns 解析后的数据或默认值
 */
export function getStorageJSON<T = any>(key: string, defaultValue: T): T {
  try {
    const item = localStorage.getItem(key)
    if (item === null) {
      return defaultValue
    }

    // 检查是否为空字符串或只有引号的字符串
    const trimmed = item.trim()
    if (trimmed === '' || trimmed === '""' || trimmed === "''") {
      console.warn(`Invalid JSON string in localStorage key "${key}":`, item)
      return defaultValue
    }

    return JSON.parse(item)
  } catch (error) {
    console.warn(`Failed to parse JSON from localStorage key "${key}":`, error)
    console.warn('Raw value:', localStorage.getItem(key))

    // 清理损坏的数据
    try {
      localStorage.removeItem(key)
    } catch (e) {
      console.error('Failed to remove corrupted localStorage item:', e)
    }

    return defaultValue
  }
}

/**
 * 安全地将数据序列化并存储到 localStorage
 * @param key - localStorage 键名
 * @param value - 要存储的数据
 * @returns 是否存储成功
 */
export function setStorageJSON(key: string, value: any): boolean {
  try {
    // 如果值为 undefined 或 null，移除该项
    if (value === undefined || value === null) {
      localStorage.removeItem(key)
      return true
    }

    const serialized = JSON.stringify(value)
    localStorage.setItem(key, serialized)
    return true
  } catch (error) {
    console.error(`Failed to save to localStorage key "${key}":`, error)
    return false
  }
}

/**
 * 安全地移除 localStorage 项
 * @param key - localStorage 键名
 * @returns 是否移除成功
 */
export function removeStorageItem(key: string): boolean {
  try {
    localStorage.removeItem(key)
    return true
  } catch (error) {
    console.error(`Failed to remove localStorage key "${key}":`, error)
    return false
  }
}

/**
 * 检查 localStorage 中的 JSON 数据是否有效
 * @param key - localStorage 键名
 * @returns 是否有效
 */
export function isValidStorageJSON(key: string): boolean {
  try {
    const item = localStorage.getItem(key)
    if (item === null) return false

    JSON.parse(item)
    return true
  } catch {
    return false
  }
}

/**
 * 清理所有损坏的 localStorage JSON 数据
 * @param keys - 要检查的键名数组，如果不提供则检查所有项
 */
export function cleanupCorruptedStorage(keys?: string[]): void {
  const keysToCheck = keys || Object.keys(localStorage)

  keysToCheck.forEach((key) => {
    try {
      const item = localStorage.getItem(key)
      if (item !== null) {
        JSON.parse(item)
      }
    } catch {
      console.warn(`Removing corrupted localStorage item: ${key}`)
      try {
        localStorage.removeItem(key)
      } catch (e) {
        console.error(`Failed to remove corrupted item ${key}:`, e)
      }
    }
  })
}
