import { handleValidationError } from './errorHandler'

export interface ValidationRule {
  required?: boolean
  min?: number
  max?: number
  pattern?: RegExp
  custom?: (value: any) => boolean | string
  message?: string
}

export interface ValidationErrors {
  [key: string]: string[]
}

export class FormValidator {
  private rules: Record<string, ValidationRule[]> = {}
  private errors: ValidationErrors = {}

  /**
   * 添加验证规则
   */
  addRule(field: string, rules: ValidationRule[]): this {
    this.rules[field] = rules
    return this
  }

  /**
   * 验证单个字段
   */
  validateField(field: string, value: any): string[] {
    const fieldRules = this.rules[field] || []
    const errors: string[] = []

    for (const rule of fieldRules) {
      const error = this.applyRule(field, value, rule)
      if (error) {
        errors.push(error)
      }
    }

    this.errors[field] = errors
    return errors
  }

  /**
   * 验证所有字段
   */
  validate(data: Record<string, any>): ValidationErrors {
    this.errors = {}

    for (const field in this.rules) {
      this.validateField(field, data[field])
    }

    return this.errors
  }

  /**
   * 应用单个规则
   */
  private applyRule(field: string, value: any, rule: ValidationRule): string | null {
    // 必填验证
    if (rule.required && this.isEmpty(value)) {
      return rule.message || `${field}是必填项`
    }

    // 如果值为空且不是必填，跳过其他验证
    if (this.isEmpty(value) && !rule.required) {
      return null
    }

    // 最小长度验证
    if (rule.min !== undefined) {
      if (typeof value === 'string' && value.length < rule.min) {
        return rule.message || `${field}最少需要${rule.min}个字符`
      }
      if (typeof value === 'number' && value < rule.min) {
        return rule.message || `${field}不能小于${rule.min}`
      }
    }

    // 最大长度验证
    if (rule.max !== undefined) {
      if (typeof value === 'string' && value.length > rule.max) {
        return rule.message || `${field}最多只能有${rule.max}个字符`
      }
      if (typeof value === 'number' && value > rule.max) {
        return rule.message || `${field}不能大于${rule.max}`
      }
    }

    // 正则表达式验证
    if (rule.pattern && typeof value === 'string') {
      if (!rule.pattern.test(value)) {
        return rule.message || `${field}格式不正确`
      }
    }

    // 自定义验证
    if (rule.custom) {
      const result = rule.custom(value)
      if (typeof result === 'string') {
        return result
      }
      if (result === false) {
        return rule.message || `${field}验证失败`
      }
    }

    return null
  }

  /**
   * 检查值是否为空
   */
  private isEmpty(value: any): boolean {
    return value === null || value === undefined || value === '' || 
           (Array.isArray(value) && value.length === 0)
  }

  /**
   * 获取错误信息
   */
  getErrors(): ValidationErrors {
    return { ...this.errors }
  }

  /**
   * 获取第一个错误信息
   */
  getFirstError(): string | null {
    for (const field in this.errors) {
      if (this.errors[field].length > 0) {
        return this.errors[field][0]
      }
    }
    return null
  }

  /**
   * 检查是否有错误
   */
  hasErrors(): boolean {
    return Object.values(this.errors).some(errors => errors.length > 0)
  }

  /**
   * 清除错误
   */
  clearErrors(): void {
    this.errors = {}
  }

  /**
   * 清除特定字段的错误
   */
  clearFieldError(field: string): void {
    delete this.errors[field]
  }
}

// 常用验证规则
export const commonRules = {
  required: { required: true },
  email: { 
    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/, 
    message: '请输入正确的邮箱格式' 
  },
  phone: { 
    pattern: /^1[3-9]\d{9}$/, 
    message: '请输入正确的手机号码' 
  },
  url: { 
    pattern: /^https?:\/\/.+/, 
    message: '请输入正确的URL格式' 
  },
  packageName: { 
    pattern: /^[a-z][a-z0-9_]*(\.[a-z][a-z0-9_]*)+$/, 
    message: '包名格式不正确，例如：com.example.app' 
  },
  version: { 
    pattern: /^\d+\.\d+\.\d+$/, 
    message: '版本号格式不正确，例如：1.0.0' 
  },
  alphanumeric: { 
    pattern: /^[a-zA-Z0-9]+$/, 
    message: '只能包含字母和数字' 
  }
}

// 便捷验证函数
export const validateForm = (data: Record<string, any>, rules: Record<string, ValidationRule[]>) => {
  const validator = new FormValidator()
  
  Object.entries(rules).forEach(([field, fieldRules]) => {
    validator.addRule(field, fieldRules)
  })
  
  const errors = validator.validate(data)
  
  // 如果有错误，发送到错误处理系统
  if (validator.hasErrors()) {
    const firstError = validator.getFirstError()
    if (firstError) {
      handleValidationError('表单验证', firstError)
    }
  }
  
  return {
    isValid: !validator.hasErrors(),
    errors,
    firstError: validator.getFirstError()
  }
}

// 验证应用创建表单
export const validateCreateAppForm = (data: {
  app_name?: string
  package_name?: string
  platform?: string
}) => {
  return validateForm(data, {
    app_name: [
      commonRules.required,
      { min: 2, max: 50, message: '应用名称长度应在2-50个字符之间' }
    ],
    package_name: [
      commonRules.required,
      commonRules.packageName
    ],
    platform: [
      commonRules.required,
      { 
        custom: (value) => ['android', 'ios'].includes(value),
        message: '平台只能是 android 或 ios'
      }
    ]
  })
}

// 验证API Key创建表单
export const validateCreateApiKeyForm = (data: {
  name?: string
  description?: string
}) => {
  return validateForm(data, {
    name: [
      commonRules.required,
      { min: 2, max: 30, message: 'API Key名称长度应在2-30个字符之间' },
      commonRules.alphanumeric
    ],
    description: [
      { max: 200, message: '描述最多200个字符' }
    ]
  })
}

