import { Message, Notification } from '@arco-design/web-vue'
import type { ApiError } from '@/types/api'

// 错误码映射
const ERROR_CODE_MAP: Record<number, string> = {
  400: '请求参数错误',
  401: '未授权访问',
  403: '权限不足',
  404: '请求的资源不存在',
  408: '请求超时',
  500: '服务器内部错误',
  502: '网关错误',
  503: '服务不可用',
  504: '网关超时'
}

// 错误类型
export enum ErrorType {
  NETWORK = 'NETWORK',
  API = 'API',
  VALIDATION = 'VALIDATION',
  UNKNOWN = 'UNKNOWN'
}

export interface AppError {
  type: ErrorType
  code: number
  message: string
  details?: any
  timestamp: string
}

export class ErrorHandler {
  private static instance: ErrorHandler
  private errorQueue: AppError[] = []
  private isOnline = navigator.onLine

  private constructor() {
    // 监听网络状态
    window.addEventListener('online', () => {
      this.isOnline = true
      this.showNetworkStatus('网络连接已恢复', 'success')
    })
    
    window.addEventListener('offline', () => {
      this.isOnline = false
      this.showNetworkStatus('网络连接已断开', 'error')
    })

    // 监听未处理的 Promise 错误
    window.addEventListener('unhandledrejection', (event) => {
      this.handleError({
        type: ErrorType.UNKNOWN,
        code: 0,
        message: '未处理的异步错误',
        details: event.reason,
        timestamp: new Date().toISOString()
      })
      event.preventDefault()
    })
  }

  static getInstance(): ErrorHandler {
    if (!ErrorHandler.instance) {
      ErrorHandler.instance = new ErrorHandler()
    }
    return ErrorHandler.instance
  }

  /**
   * 处理 API 错误
   */
  handleApiError(error: any): AppError {
    let appError: AppError

    if (error.response) {
      // 服务器响应了错误状态码
      const { status, data } = error.response
      appError = {
        type: ErrorType.API,
        code: status,
        message: data?.message || ERROR_CODE_MAP[status] || '服务器错误',
        details: data,
        timestamp: new Date().toISOString()
      }
    } else if (error.request) {
      // 请求发出但没有收到响应
      appError = {
        type: ErrorType.NETWORK,
        code: 0,
        message: this.isOnline ? '网络连接超时' : '网络连接已断开',
        details: error.request,
        timestamp: new Date().toISOString()
      }
    } else {
      // 其他错误
      appError = {
        type: ErrorType.UNKNOWN,
        code: 0,
        message: error.message || '未知错误',
        details: error,
        timestamp: new Date().toISOString()
      }
    }

    this.handleError(appError)
    return appError
  }

  /**
   * 处理验证错误
   */
  handleValidationError(field: string, message: string): AppError {
    const appError: AppError = {
      type: ErrorType.VALIDATION,
      code: 422,
      message: `${field}: ${message}`,
      details: { field, message },
      timestamp: new Date().toISOString()
    }

    this.handleError(appError)
    return appError
  }

  /**
   * 统一错误处理
   */
  private handleError(error: AppError): void {
    // 添加到错误队列
    this.errorQueue.push(error)
    
    // 保持错误队列大小
    if (this.errorQueue.length > 100) {
      this.errorQueue.shift()
    }

    // 显示错误消息
    this.showErrorMessage(error)

    // 记录错误日志
    this.logError(error)

    // 特殊错误处理
    this.handleSpecialErrors(error)
  }

  /**
   * 显示错误消息
   */
  private showErrorMessage(error: AppError): void {
    switch (error.type) {
      case ErrorType.API:
        if (error.code === 401) {
          Notification.error({
            title: '认证失败',
            content: '登录已过期，请重新登录',
            duration: 5000
          })
          // 可以在这里添加重新登录逻辑
          this.redirectToLogin()
        } else if (error.code >= 500) {
          Notification.error({
            title: '服务器错误',
            content: error.message,
            duration: 5000
          })
        } else {
          Message.error(error.message)
        }
        break

      case ErrorType.NETWORK:
        Notification.warning({
          title: '网络错误',
          content: error.message,
          duration: 3000
        })
        break

      case ErrorType.VALIDATION:
        Message.warning(error.message)
        break

      case ErrorType.UNKNOWN:
        console.error('未知错误:', error)
        Message.error('发生了未知错误，请稍后重试')
        break
    }
  }

  /**
   * 显示网络状态
   */
  private showNetworkStatus(message: string, type: 'success' | 'error'): void {
    if (type === 'success') {
      Message.success(message)
    } else {
      Message.error(message)
    }
  }

  /**
   * 记录错误日志
   */
  private logError(error: AppError): void {
    // 开发环境打印详细错误
    if (import.meta.env.DEV) {
      console.group(`🚨 ${error.type} Error`)
      console.error('Code:', error.code)
      console.error('Message:', error.message)
      console.error('Details:', error.details)
      console.error('Timestamp:', error.timestamp)
      console.groupEnd()
    }

    // 生产环境可以发送到日志服务
    if (import.meta.env.PROD) {
      this.sendErrorToLoggingService(error)
    }
  }

  /**
   * 发送错误到日志服务
   */
  private async sendErrorToLoggingService(error: AppError): Promise<void> {
    try {
      // 这里可以集成第三方日志服务，如 Sentry
      // await logService.error(error)
    } catch (e) {
      console.error('Failed to send error to logging service:', e)
    }
  }

  /**
   * 处理特殊错误
   */
  private handleSpecialErrors(error: AppError): void {
    // 401 未授权 - 清除本地状态并重定向
    if (error.code === 401) {
      this.clearAuthState()
    }

    // 403 权限不足 - 可以显示权限不足页面
    if (error.code === 403) {
      // router.push('/forbidden')
    }
  }

  /**
   * 清除认证状态
   */
  private clearAuthState(): void {
    // 清除 cookie
    document.cookie = 'z_token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT'
    
    // 清除 localStorage
    localStorage.removeItem('user')
    localStorage.removeItem('theme-mode')
  }

  /**
   * 重定向到登录页
   */
  private redirectToLogin(): void {
    setTimeout(() => {
      window.location.href = import.meta.env.VITE_OATH_URL || '/'
    }, 2000)
  }

  /**
   * 获取错误历史
   */
  getErrorHistory(): AppError[] {
    return [...this.errorQueue]
  }

  /**
   * 清除错误历史
   */
  clearErrorHistory(): void {
    this.errorQueue = []
  }

  /**
   * 重试操作
   */
  async retry<T>(operation: () => Promise<T>, maxRetries = 3): Promise<T> {
    let lastError: any
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation()
      } catch (error) {
        lastError = error
        
        if (attempt === maxRetries) {
          this.handleApiError(error)
          throw error
        }
        
        // 延迟重试
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000))
      }
    }
    
    throw lastError
  }
}

// 导出单例实例
export const errorHandler = ErrorHandler.getInstance()

// 便捷方法
export const handleApiError = (error: any) => errorHandler.handleApiError(error)
export const handleValidationError = (field: string, message: string) => 
  errorHandler.handleValidationError(field, message)
export const retryOperation = <T>(operation: () => Promise<T>, maxRetries?: number) => 
  errorHandler.retry(operation, maxRetries)

