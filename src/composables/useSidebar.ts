import { ref, onMounted, onBeforeUnmount } from 'vue'

export function useSidebar() {
  const sidebarOpen = ref(false)
  const isAnimating = ref(false)

  // 键盘快捷键支持
  const handleKeydown = (event: KeyboardEvent) => {
    // ESC 键关闭侧边栏
    if (event.key === 'Escape' && sidebarOpen.value) {
      closeSidebar()
    }

    // Ctrl/Cmd + [ 切换侧边栏
    if ((event.ctrlKey || event.metaKey) && event.key === '[') {
      event.preventDefault()
      toggleSidebar()
    }
  }

  // 触摸事件处理
  let touchStartX = 0
  let touchStartY = 0
  let touchMoved = false

  const handleTouchStart = (event: TouchEvent) => {
    touchStartX = event.touches[0].clientX
    touchStartY = event.touches[0].clientY
    touchMoved = false
  }

  const handleTouchMove = (event: TouchEvent) => {
    if (!touchStartX) return

    const touchCurrentX = event.touches[0].clientX
    const touchCurrentY = event.touches[0].clientY

    const diffX = touchStartX - touchCurrentX
    const diffY = touchStartY - touchCurrentY

    // 如果是水平滑动且距离足够
    if (Math.abs(diffX) > Math.abs(diffY) && Math.abs(diffX) > 10) {
      touchMoved = true

      // 从左边缘向右滑动打开侧边栏
      if (touchStartX < 20 && diffX < -50) {
        event.preventDefault()
        openSidebar()
      }
      // 在侧边栏内向左滑动关闭侧边栏
      else if (sidebarOpen.value && diffX > 50) {
        event.preventDefault()
        closeSidebar()
      }
    }
  }

  const handleTouchEnd = () => {
    touchStartX = 0
    touchStartY = 0
    touchMoved = false
  }

  const openSidebar = () => {
    if (isAnimating.value) return
    isAnimating.value = true
    sidebarOpen.value = true

    // 动画完成后重置状态
    setTimeout(() => {
      isAnimating.value = false
    }, 300)
  }

  const closeSidebar = () => {
    if (isAnimating.value) return
    isAnimating.value = true
    sidebarOpen.value = false

    // 动画完成后重置状态
    setTimeout(() => {
      isAnimating.value = false
    }, 250)
  }

  const toggleSidebar = () => {
    if (sidebarOpen.value) {
      closeSidebar()
    } else {
      openSidebar()
    }
  }

  // 防止背景滚动
  const preventScroll = (e: Event) => {
    if (sidebarOpen.value) {
      e.preventDefault()
    }
  }

  onMounted(() => {
    // 添加键盘监听
    document.addEventListener('keydown', handleKeydown)

    // 添加触摸监听
    document.addEventListener('touchstart', handleTouchStart, { passive: false })
    document.addEventListener('touchmove', handleTouchMove, { passive: false })
    document.addEventListener('touchend', handleTouchEnd)

    // 防止背景滚动
    document.addEventListener('touchmove', preventScroll, { passive: false })
  })

  onBeforeUnmount(() => {
    // 清理监听器
    document.removeEventListener('keydown', handleKeydown)
    document.removeEventListener('touchstart', handleTouchStart)
    document.removeEventListener('touchmove', handleTouchMove)
    document.removeEventListener('touchend', handleTouchEnd)
    document.removeEventListener('touchmove', preventScroll)
  })

  return {
    sidebarOpen,
    isAnimating,
    openSidebar,
    closeSidebar,
    toggleSidebar
  }
}
