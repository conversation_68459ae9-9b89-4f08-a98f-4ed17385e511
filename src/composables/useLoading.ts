import { ref, computed } from 'vue'

export interface LoadingState {
  [key: string]: boolean
}

export function useLoading(initialStates: string[] = []) {
  const loadingStates = ref<LoadingState>({})

  // 初始化状态
  initialStates.forEach((state) => {
    loadingStates.value[state] = false
  })

  // 设置单个加载状态
  const setLoading = (key: string, value: boolean) => {
    loadingStates.value[key] = value
  }

  // 开始加载
  const startLoading = (key: string) => {
    setLoading(key, true)
  }

  // 停止加载
  const stopLoading = (key: string) => {
    setLoading(key, false)
  }

  // 获取单个加载状态
  const isLoading = (key: string) => {
    return computed(() => loadingStates.value[key] || false)
  }

  // 检查是否有任何加载状态
  const hasAnyLoading = computed(() => {
    return Object.values(loadingStates.value).some((state) => state)
  })

  // 批量设置加载状态
  const setMultipleLoading = (states: Record<string, boolean>) => {
    Object.entries(states).forEach(([key, value]) => {
      loadingStates.value[key] = value
    })
  }

  // 异步操作包装器
  const withLoading = async <T>(
    key: string,
    asyncFn: () => Promise<T>,
    options?: {
      onStart?: () => void
      onEnd?: () => void
      onError?: (error: any) => void
    }
  ): Promise<T> => {
    try {
      startLoading(key)
      options?.onStart?.()
      const result = await asyncFn()
      return result
    } catch (error) {
      options?.onError?.(error)
      throw error
    } finally {
      stopLoading(key)
      options?.onEnd?.()
    }
  }

  // 重置所有加载状态
  const resetLoading = () => {
    Object.keys(loadingStates.value).forEach((key) => {
      loadingStates.value[key] = false
    })
  }

  return {
    loadingStates: computed(() => loadingStates.value),
    setLoading,
    startLoading,
    stopLoading,
    isLoading,
    hasAnyLoading,
    setMultipleLoading,
    withLoading,
    resetLoading
  }
}

// 全局加载状态管理
const globalLoadingStates = ref<LoadingState>({})

export function useGlobalLoading() {
  const setGlobalLoading = (key: string, value: boolean) => {
    globalLoadingStates.value[key] = value
  }

  const isGlobalLoading = (key: string) => {
    return computed(() => globalLoadingStates.value[key] || false)
  }

  const hasAnyGlobalLoading = computed(() => {
    return Object.values(globalLoadingStates.value).some((state) => state)
  })

  return {
    globalLoadingStates: computed(() => globalLoadingStates.value),
    setGlobalLoading,
    isGlobalLoading,
    hasAnyGlobalLoading
  }
}
