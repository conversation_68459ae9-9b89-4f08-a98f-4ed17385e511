@tailwind base;
@tailwind components;
@tailwind utilities;

/* 全局CSS变量 - 支持暗黑模式 */
:root {
  /* 浅色模式颜色变量 */
  --color-bg-primary: #ffffff;
  --color-bg-secondary: #f8fafc;
  --color-bg-tertiary: #f1f5f9;
  --color-text-primary: #1f2937;
  --color-text-secondary: #6b7280;
  --color-text-tertiary: #9ca3af;
  --color-border-primary: #e5e7eb;
  --color-border-secondary: #d1d5db;
  --color-shadow: rgba(0, 0, 0, 0.1);
  --color-shadow-hover: rgba(0, 0, 0, 0.15);
}

.dark {
  /* 暗黑模式颜色变量 */
  --color-bg-primary: #0f172a;
  --color-bg-secondary: #1e293b;
  --color-bg-tertiary: #334155;
  --color-text-primary: #f8fafc;
  --color-text-secondary: #e2e8f0;
  --color-text-tertiary: #94a3b8;
  --color-border-primary: #334155;
  --color-border-secondary: #475569;
  --color-shadow: rgba(0, 0, 0, 0.3);
  --color-shadow-hover: rgba(0, 0, 0, 0.4);
}

/* 全局基础样式 */
* {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

html {
  scroll-behavior: smooth;
}

body {
  background-color: var(--color-bg-primary);
  color: var(--color-text-primary);
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--color-bg-secondary);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: var(--color-text-tertiary);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-text-secondary);
}

.dark ::-webkit-scrollbar-track {
  background: var(--color-bg-tertiary);
}

.dark ::-webkit-scrollbar-thumb {
  background: var(--color-text-tertiary);
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: var(--color-text-secondary);
}
