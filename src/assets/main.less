@import './style.css';

#app {
  height: 100%;
  width: 100%;
  overflow: hidden;
  background-color: var(--color-bg-primary);
  color: var(--color-text-primary);
  transition: background-color 0.3s ease, color 0.3s ease;
}

a,
.green {
  text-decoration: none;
  color: hsla(160, 100%, 37%, 1);
  transition: 0.4s;
}

/* Arco Design 主题适配 */
.arco-menu-dark {
  background: #4b7bff !important;
  .arco-menu-item {
    background: #4b7bff !important;
  }
  .arco-menu-item:hover {
    color: white !important;
  }
  .arco-menu-selected-label {
    background: white !important;
  }
}

/* 暗黑模式下的Arco Design组件样式覆盖 */
.dark {
  /* 表格样式 */
  .arco-table {
    background-color: var(--color-bg-secondary) !important;
    color: var(--color-text-primary) !important;
  }

  .arco-table-th {
    background-color: var(--color-bg-tertiary) !important;
    color: var(--color-text-primary) !important;
    border-color: var(--color-border-primary) !important;
  }

  .arco-table-td {
    background-color: var(--color-bg-secondary) !important;
    color: var(--color-text-primary) !important;
    border-color: var(--color-border-primary) !important;
  }

  .arco-table-tr:hover .arco-table-td {
    background-color: var(--color-bg-tertiary) !important;
  }

  /* 按钮样式 */
  .arco-btn-secondary {
    background-color: var(--color-bg-tertiary) !important;
    color: var(--color-text-primary) !important;
    border-color: var(--color-border-primary) !important;
  }

  .arco-btn-secondary:hover {
    background-color: var(--color-bg-primary) !important;
    border-color: var(--color-border-secondary) !important;
  }

  /* 输入框样式 */
  .arco-input {
    background-color: var(--color-bg-tertiary) !important;
    color: var(--color-text-primary) !important;
    border-color: var(--color-border-primary) !important;
  }

  .arco-input:focus {
    border-color: #3b82f6 !important;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2) !important;
  }

  /* 模态框样式 */
  .arco-modal {
    background-color: var(--color-bg-secondary) !important;
    color: var(--color-text-primary) !important;
  }

  .arco-modal-header {
    background-color: var(--color-bg-secondary) !important;
    color: var(--color-text-primary) !important;
    border-color: var(--color-border-primary) !important;
  }

  .arco-modal-body {
    background-color: var(--color-bg-secondary) !important;
    color: var(--color-text-primary) !important;
  }

  .arco-modal-footer {
    background-color: var(--color-bg-secondary) !important;
    border-color: var(--color-border-primary) !important;
  }

  /* 选择器样式 */
  .arco-select-view {
    background-color: var(--color-bg-tertiary) !important;
    color: var(--color-text-primary) !important;
    border-color: var(--color-border-primary) !important;
  }

  .arco-select-dropdown {
    background-color: var(--color-bg-secondary) !important;
    border-color: var(--color-border-primary) !important;
  }

  .arco-select-option {
    color: var(--color-text-primary) !important;
  }

  .arco-select-option:hover {
    background-color: var(--color-bg-tertiary) !important;
  }

  /* 分页器样式 */
  .arco-pagination-item {
    background-color: var(--color-bg-tertiary) !important;
    color: var(--color-text-primary) !important;
    border-color: var(--color-border-primary) !important;
  }

  .arco-pagination-item:hover {
    background-color: var(--color-bg-primary) !important;
    border-color: var(--color-border-secondary) !important;
  }

  .arco-pagination-item-active {
    background-color: #3b82f6 !important;
    color: white !important;
    border-color: #3b82f6 !important;
  }
}
