import type { AxiosResponse } from 'axios'

const fullfilled = async (res: AxiosResponse): Promise<AxiosResponse> => {
  return res
}
const rejected = async (err: any) => {
  if (err.response.status === 401) {
    document.cookie = 'z_token=; path=/;'
    localStorage.removeItem('user-info')
    localStorage.removeItem('tab_page')
    window.location.href = '/'
  }
  return Promise.reject(err)
}

export default {
  fullfilled,
  rejected
}
