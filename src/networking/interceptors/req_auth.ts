import type { AxiosRequestConfig } from 'axios'

const fullfilled = async (config: AxiosRequestConfig<any>) => {
  console.log(config.url)
  if (config.url!.indexOf('/login') >= 0) {
    return config
  } else {
    console.log('need token')
    const token = document.cookie.replace(
      /(?:(?:^|.*;\s*)z_token\s*=\s*([^;]*).*$)|^.*$/,
      '$1'
    )
    if (token) {
      config.headers!['api-token'] = token
    } else {
      window.location.href = import.meta.env.VITE_OATH_URL
    }
  }
  return config
}
const rejected = async (err: any) => {
  return Promise.reject(err)
}
export default {
  fullfilled,
  rejected
}
