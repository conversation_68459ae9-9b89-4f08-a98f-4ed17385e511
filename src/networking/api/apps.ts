import { rClient } from './basicClient'
import type {
  ApiResponse,
  App,
  CreateAppRequest,
  CreatePublishRequest,
  UpdatePublishRequest,
  PublishPackageRequest,
  ActivePublishRequest,
  GetDownloadUrlRequest,
  DeleteAppRequest,
  DeleteP<PERSON>lishRequest,
  UploadPackagesRequest,
  PackageFile,
  PublishInfo
} from '@/types/api'

export const allApps = (): Promise<ApiResponse<App[]>> => {
  return rClient.get('/upgradeAdmin/allAPP')
}



export const allUploadPackages = (
  request: UploadPackagesRequest
): Promise<ApiResponse<PackageFile[]>> => {
  return rClient.post('/upgradeAdmin/getUploadFiles', request)
}




export const createPublish = (
  request: CreatePublishRequest
): Promise<ApiResponse<PublishInfo>> => {
  return rClient.post('/upgradeAdmin/createPublish', request)
}

export const publishPackage = (
  request: PublishPackageRequest
): Promise<ApiResponse<any>> => {
  return rClient.post('/upgradeAdmin/publihPackage', request)
}

export const allPublish = (
  request: UploadPackagesRequest
): Promise<ApiResponse<PublishInfo[]>> => {
  return rClient.post('/upgradeAdmin/allPublish', request)
}

export const activePublish = (
  request: ActivePublishRequest
): Promise<ApiResponse<any>> => {
  return rClient.post('/upgradeAdmin/activatePackage', request)
}

export const getDownloadUrlByHash = (
  request: GetDownloadUrlRequest
): Promise<ApiResponse<{ downloadUrl: string }>> => {
  return rClient.post('/upgradeAdmin/getDownloadUrl', request)
}

export const deleteApp = (
  request: DeleteAppRequest
): Promise<ApiResponse<any>> => {
  return rClient.post('/upgradeAdmin/deleteAPP', request)
}

export const createApp = (
  request: CreateAppRequest
): Promise<ApiResponse<App>> => {
  return rClient.post('/upgradeAdmin/createAPP', request)
}

export const deletePublish = (
  request: DeletePublishRequest
): Promise<ApiResponse<any>> => {
  return rClient.post('/upgradeAdmin/deletePublish', request)
}

export const updatePublish = (
  request: UpdatePublishRequest
): Promise<ApiResponse<PublishInfo>> => {
  return rClient.post(`/upgradeAdmin/updatePublish/${request.publish_id}`, request)
}
