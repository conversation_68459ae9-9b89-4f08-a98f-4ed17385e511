import { rClient } from './basicClient'

export const allApps = () => {
  return rClient.get('/upgradeAdmin/allAPP')
}

export const allUploadPackages = (package_name: string, platform: string) => {
  return rClient.post('/upgradeAdmin/getUploadFiles', { package_name, platform })
}

export const createPublish = (
  package_name: string,
  platform: string,
  channel: string,
  update_type: number,
  version_name: string,
  version_code: string,
  upgrade_features: string,
  package_downloadUrl: string,
  official_site: string,
  locale: string,
  active: number,
  min_binary_version_code: string,
  ext: string
) => {
  return rClient.post('/upgradeAdmin/createPublish', {
    package_name,
    platform,
    channel,
    update_type,
    version_name,
    version_code,
    upgrade_features,
    package_downloadUrl,
    official_site,
    locale,
    active,
    min_binary_version_code,
    ext
  })
}

export const publihPackage = (
  package_id: string,
  upgrade_features: string,
  update_type: number,
  package_downloadUrl: string,
  official_site: string,
  locale: string
) => {
  return rClient.post('/upgradeAdmin/publihPackage', {
    package_id,
    upgrade_features,
    update_type,
    package_downloadUrl,
    official_site,
    locale
  })
}

export const allPublish = (package_name: string, platform: string) => {
  return rClient.post('/upgradeAdmin/allPublish', { package_name, platform })
}

export const activePublish = (publih_id: string, active: string) => {
  return rClient.post('/upgradeAdmin/activatePackage', {
    publih_id,
    active
  })
}

export const getDownloadUrlByHash = (file_hash: string) => {
  return rClient.post('/upgradeAdmin/getDownloadUrl', { file_hash })
}

export const deleteApp = (app_id: string) => {
  return rClient.post('/upgradeAdmin/deleteAPP', { app_id })
}

export const createApp = (appData: {
  app_name: string
  package_name: string
  platform: string
}) => {
  return rClient.post('/upgradeAdmin/createAPP', appData)
}

export const deletePublish = (publish_id: string) => {
  return rClient.post('/upgradeAdmin/deletePublish', { publish_id })
}

export const updatePublish = (
  publish_id: string,
  package_name: string,
  platform: string,
  channel: string,
  update_type: number,
  version_name: string,
  version_code: string,
  upgrade_features: string,
  package_downloadUrl: string,
  official_site: string,
  locale: string,
  active: number,
  min_binary_version_code: string,
  ext: string
) => {
  return rClient.post(`/upgradeAdmin/updatePublish/${publish_id}`, {
    package_name,
    platform,
    channel,
    update_type,
    version_name,
    version_code,
    upgrade_features,
    package_downloadUrl,
    official_site,
    locale,
    active,
    min_binary_version_code,
    ext
  })
}
