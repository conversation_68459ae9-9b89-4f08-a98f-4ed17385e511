import { rClient } from './basicClient'

export const createApiKey = (params: any) => {
  return rClient.post('/user/genApiKey', params)
}
export const getUserDetail = () => {
  return rClient.post('/user/getUserDetail', {})
}
export const getAllApiKeys = () => {
  return rClient.get('/user/allApiKey')
}
export const deleteKey = (params: any) => {
  return rClient.post('/user/deleteApiKey', params)
}
export const login = (params: any) => {
  return rClient.post('/user/login', params)
}

export const logout = () => {
  return rClient.get('/user/logout')
}
