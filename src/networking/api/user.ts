import { rClient } from './basicClient'
import type {
  ApiResponse,
  User,
  LoginRequest,
  LoginResponse
} from '@/types/api'

// API Key 相关类型
interface ApiKey {
  id: string
  name: string
  key: string
  created_at: string
  last_used?: string
}

interface CreateApiKeyRequest {
  name: string
  description?: string
}

interface DeleteApiKeyRequest {
  key_id: string
}

export const createApiKey = (
  request: CreateApiKeyRequest
): Promise<ApiResponse<ApiKey>> => {
  return rClient.post('/user/genApiKey', request)
}

export const getUserDetail = (): Promise<ApiResponse<User>> => {
  return rClient.post('/user/getUserDetail', {})
}

export const getAllApiKeys = (): Promise<ApiResponse<ApiKey[]>> => {
  return rClient.get('/user/allApiKey')
}

export const deleteKey = (
  request: DeleteApiKeyRequest
): Promise<ApiResponse<any>> => {
  return rClient.post('/user/deleteApiKey', request)
}

export const login = (
  request: LoginRequest
): Promise<ApiResponse<LoginResponse>> => {
  return rClient.post('/user/login', request)
}

export const logout = (): Promise<ApiResponse<any>> => {
  return rClient.get('/user/logout')
}
