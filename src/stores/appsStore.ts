import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { appsApi } from '@/networking/api'
import { getStorageJSON, setStorageJSON } from '@/utils/localStorage'
import type { App } from '@/types/api'

export const useAppsStore = defineStore('apps', () => {
  const apps = ref<App[]>([])
  const activeApp = ref<App | { app_name: string }>(
    getStorageJSON('active_app', { app_name: '请选择APP' })
  )

  async function reloadApps() {
    const ret = await appsApi.allApps()
    apps.value = ret.data
    console.log('reloadApps apps.value', apps.value)
  }

  function setActiveApp(app: any) {
    console.log('setActiveApp', app)
    activeApp.value = app
    setStorageJSON('active_app', app)
  }

  return { activeApp, apps, reloadApps, setActiveApp }
})
