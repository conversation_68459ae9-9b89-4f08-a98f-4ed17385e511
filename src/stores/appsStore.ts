import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { appsApi } from '@/networking/api'
export const useAppsStore = defineStore('apps', () => {
  const apps = ref([] as any[])
  const activeApp = ref(
    JSON.parse(localStorage.getItem('active_app') as string) ||
      ({ app_name: '请选择APP' } as any)
  )

  async function reloadApps() {
    const ret = await appsApi.allApps()
    apps.value = ret.data
    console.log('reloadApps apps.value', apps.value)
  }

  function setActiveApp(app: any) {
    console.log('setActiveApp', app)
    localStorage.setItem('active_app', JSON.stringify(app))
  }

  return { activeApp, apps, reloadApps, setActiveApp }
})
