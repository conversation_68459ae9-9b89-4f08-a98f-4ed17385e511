import { defineStore } from 'pinia'
import { ref, computed, watch } from 'vue'

export type ThemeMode = 'light' | 'dark' | 'system'

export const useThemeStore = defineStore('theme', () => {
  // 当前主题模式
  const mode = ref<ThemeMode>('system')
  
  // 系统是否为暗黑模式
  const systemIsDark = ref(false)
  
  // 实际应用的主题（考虑系统主题）
  const isDark = computed(() => {
    if (mode.value === 'system') {
      return systemIsDark.value
    }
    return mode.value === 'dark'
  })
  
  // 初始化主题
  const initTheme = () => {
    // 从localStorage读取保存的主题设置
    const savedTheme = localStorage.getItem('theme-mode') as ThemeMode
    if (savedTheme && ['light', 'dark', 'system'].includes(savedTheme)) {
      mode.value = savedTheme
    }
    
    // 检测系统主题
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
    systemIsDark.value = mediaQuery.matches
    
    // 监听系统主题变化
    mediaQuery.addEventListener('change', (e) => {
      systemIsDark.value = e.matches
    })
    
    // 应用主题
    applyTheme()
  }
  
  // 应用主题到DOM
  const applyTheme = () => {
    const html = document.documentElement
    const body = document.body

    console.log('Applying theme, isDark:', isDark.value)

    if (isDark.value) {
      html.classList.add('dark')
      html.classList.remove('light')
      body.setAttribute('arco-theme', 'dark')
    } else {
      html.classList.add('light')
      html.classList.remove('dark')
      body.removeAttribute('arco-theme')
    }

    // 强制触发重新渲染
    html.style.colorScheme = isDark.value ? 'dark' : 'light'

    console.log('HTML classes:', html.className)
    console.log('Body arco-theme:', body.getAttribute('arco-theme'))
  }
  
  // 切换主题模式
  const setTheme = (newMode: ThemeMode) => {
    console.log('Setting theme to:', newMode)
    mode.value = newMode
    localStorage.setItem('theme-mode', newMode)
    applyTheme()
    console.log('Theme applied, isDark:', isDark.value)
  }
  
  // 切换暗黑模式（在light和dark之间切换）
  const toggleDark = () => {
    if (mode.value === 'system') {
      // 如果当前是系统模式，根据系统当前状态切换到相反模式
      setTheme(systemIsDark.value ? 'light' : 'dark')
    } else {
      // 在light和dark之间切换
      setTheme(mode.value === 'light' ? 'dark' : 'light')
    }
  }
  
  // 监听isDark变化，自动应用主题
  watch(isDark, () => {
    applyTheme()
  }, { immediate: true })
  
  // 获取主题显示名称
  const getThemeDisplayName = computed(() => {
    switch (mode.value) {
      case 'light':
        return '浅色模式'
      case 'dark':
        return '深色模式'
      case 'system':
        return '跟随系统'
      default:
        return '跟随系统'
    }
  })
  
  // 获取当前主题图标
  const getThemeIcon = computed(() => {
    if (mode.value === 'system') {
      return isDark.value ? 'moon' : 'sun'
    }
    return mode.value === 'dark' ? 'moon' : 'sun'
  })
  
  return {
    mode,
    isDark,
    systemIsDark,
    initTheme,
    setTheme,
    toggleDark,
    getThemeDisplayName,
    getThemeIcon
  }
})
