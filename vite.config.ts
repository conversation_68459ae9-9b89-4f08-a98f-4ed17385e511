import { fileURLToPath, URL } from 'node:url'

import { defineConfig, loadEnv } from 'vite'
import type { ConfigEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import babel from '@rollup/plugin-babel'

import copyPlugin from 'rollup-plugin-copy'
export default defineConfig((env: ConfigEnv) => {
  const viteEnv = loadEnv(env.mode, process.cwd())
  return {
    base: viteEnv.VITE_BASE || './',
    define: {
      // Vue feature flags
      __VUE_PROD_HYDRATION_MISMATCH_DETAILS__: 'false',
      __VUE_OPTIONS_API__: 'true',
      __VUE_PROD_DEVTOOLS__: 'false'
    },
    plugins: [
      vue(),
      babel({
        babelHelpers: 'bundled',
        include: [/\.vue$/, /\.ts$/],
        extensions: ['.vue', '.ts', '.js'],
        exclude: 'node_modules/**',
        plugins: [
          '@babel/plugin-proposal-optional-chaining',
          '@babel/plugin-proposal-nullish-coalescing-operator'
        ]
      }),
      copyPlugin({
        targets: [{ src: 'static', dest: 'dist' }]
      })
    ],
    optimizeDeps: {
      esbuildOptions: { target: 'es2020' },
      include: ['axios', 'qs']
    },
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url))
      }
    },
    server: {
      host: '0.0.0.0',
      port: 8081,
      proxy: {
        '/user': {
          target: 'https://api-upgrade.zen5dev.com',
          changeOrigin: true,
          secure: false,
          headers: {}
        },
        '/api': {
          target: 'https://api-upgrade.zen5dev.com',
          changeOrigin: true,
          secure: false,
          headers: {}
        }
      }
    },
    build: {
      target: ['es2020', 'chrome70']
    }
  }
})
