export default  {
  testTimeout: 300000,
  verbose: false,
  collectCoverage: true,
  preset: "ts-jest",
  testMatch: [
    "**/?(*.)+(spec|test).+(ts|tsx|js)"
  ],
	transform: {
		"^.+\\.(t|j)s$": "ts-jest"
	},
  moduleNameMapper: {
    "^@/(.*)$": "<rootDir>/$1"
  },
  "moduleFileExtensions": [
    "js",
    "json",
    "ts"
  ],
  // "resetMocks": false,
  // "setupFiles": ["jest-localstorage-mock"],
  roots:['src','rig_dev'],
  // rootDir: "./",
  collectCoverageFrom: [
    "**/*.(t|j)s"
  ],
  coverageDirectory: "../coverage",
  testEnvironment: "node"
};
