/** @type {import('tailwindcss').Config} */
export default {
  content: ['./index.html', './src/**/*.{vue,js,ts,jsx,tsx}'],
  darkMode: 'class', // 启用基于class的暗黑模式
  theme: {
    extend: {
      colors: {
        // 自定义暗黑模式颜色
        dark: {
          50: '#f8fafc',
          100: '#f1f5f9',
          200: '#e2e8f0',
          300: '#cbd5e1',
          400: '#94a3b8',
          500: '#64748b',
          600: '#475569',
          700: '#334155',
          800: '#1e293b',
          900: '#0f172a',
          950: '#020617'
        },
        // 主题色适配
        primary: {
          50: '#eff6ff',
          100: '#dbeafe',
          200: '#bfdbfe',
          300: '#93c5fd',
          400: '#60a5fa',
          500: '#3b82f6',
          600: '#2563eb',
          700: '#1d4ed8',
          800: '#1e40af',
          900: '#1e3a8a',
          950: '#172554'
        }
      },
      backgroundColor: {
        // 暗黑模式背景色
        'dark-primary': '#0f172a',
        'dark-secondary': '#1e293b',
        'dark-tertiary': '#334155'
      },
      textColor: {
        // 暗黑模式文字色
        'dark-primary': '#f8fafc',
        'dark-secondary': '#e2e8f0',
        'dark-tertiary': '#94a3b8'
      },
      borderColor: {
        // 暗黑模式边框色
        'dark-primary': '#334155',
        'dark-secondary': '#475569'
      }
    }
  },
  plugins: [require('@tailwindcss/forms')]
}
