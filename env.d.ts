/// <reference types="vite/client" />
interface ImportMetaEnv {
  readonly VITE_OATH_URL: string;
  readonly VITE_COMMON_ENDPOINT:string
  readonly VITE_MEETING_API: string;
  readonly VITE_MEETING_APP_KEY: string;
  readonly VITE_RTC_APPID: number;
  readonly VITE_RTC_ENV: string;
  readonly VITE_RTC_APP_KEY: string;
  readonly VITE_LOG_ENDPOINT:string
  readonly VITE_LOG_CLOSE: string;
  readonly VITE_MANUAL_URL: string;
  readonly VITE_BASE: string;
  readonly VITE_AGREEMENT_URL: string;
  readonly VITE_PRIVACY_URL: string;
  readonly VITE_IOS_SCHEME:string
  readonly VITE_ANDROID_SCHEME:string
  readonly VITE_APK_URL: string;
  readonly VITE_WITH_CREDENTIALS: string;

}

interface ImportMeta {
  readonly env: ImportMetaEnv;
}
