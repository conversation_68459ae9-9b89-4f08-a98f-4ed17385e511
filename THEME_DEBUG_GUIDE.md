# 主题切换调试指南

## 修复的问题

### 1. 响应式状态问题
- ✅ 修复了 `storeToRefs` 的使用，确保组件能正确响应主题变化
- ✅ 分离了方法和响应式状态的解构

### 2. 主题初始化时机
- ✅ 在 Pinia 安装后立即初始化主题
- ✅ 移除了 Layout 组件中的重复初始化

### 3. Arco Design 主题支持
- ✅ 导入了 Arco Design 暗黑主题 CSS
- ✅ 使用 `body[arco-theme='dark']` 选择器确保样式正确应用

### 4. DOM 更新优化
- ✅ 添加了 `colorScheme` 样式强制重新渲染
- ✅ 确保 HTML 类名和 body 属性同步更新

## 测试步骤

### 1. 打开浏览器开发者工具
```
F12 -> Console 标签页
```

### 2. 观察主题切换日志
点击侧边栏的主题切换按钮，应该看到类似输出：
```
Setting theme to: dark
Applying theme, isDark: true
HTML classes: dark
Body arco-theme: dark
Theme applied, isDark: true
```

### 3. 检查 DOM 变化
在 Elements 标签页中观察：
- `<html>` 元素应该有 `dark` 或 `light` 类名
- `<body>` 元素应该有或没有 `arco-theme="dark"` 属性

### 4. 验证样式应用
- 背景色应该立即改变
- 文字颜色应该立即改变
- 表格和其他组件应该立即切换主题

## 如果仍有问题

### 检查控制台错误
```javascript
// 在控制台手动测试
const themeStore = window.__VUE_DEVTOOLS_GLOBAL_HOOK__.apps[0].config.globalProperties.$pinia._s.get('theme')
console.log('Current theme mode:', themeStore.mode)
console.log('Is dark:', themeStore.isDark)
```

### 手动切换主题
```javascript
// 在控制台手动切换
themeStore.setTheme('dark')
themeStore.setTheme('light')
```

### 检查 CSS 加载
确保以下 CSS 文件已加载：
- `@arco-design/web-vue/dist/arco.css`
- `@arco-design/web-vue/dist/arco.dark.css`

## 预期行为

### 主题切换应该：
1. ✅ 立即生效，无需刷新页面
2. ✅ 保存到 localStorage
3. ✅ 更新所有页面组件
4. ✅ 正确显示主题图标和文字
5. ✅ 平滑的过渡动画

### 页面刷新后应该：
1. ✅ 保持之前选择的主题
2. ✅ 正确初始化主题状态
3. ✅ 显示正确的主题图标

## 常见问题排查

### 问题1：切换后部分组件没有变化
**解决方案：** 检查组件是否正确使用了 `storeToRefs`

### 问题2：刷新页面后主题丢失
**解决方案：** 检查 localStorage 和初始化逻辑

### 问题3：Arco Design 组件主题不变
**解决方案：** 确保导入了暗黑主题 CSS 并正确设置了 body 属性

### 问题4：主题图标状态不正确
**解决方案：** 检查 ThemeToggle 组件的响应式状态解构
