# 暗黑模式功能说明

## 已实现的功能

### 1. 主题管理系统
- ✅ 创建了 `useThemeStore` Pinia store
- ✅ 支持三种主题模式：浅色、深色、跟随系统
- ✅ 本地存储持久化主题设置
- ✅ 自动检测系统主题变化

### 2. 主题切换组件
- ✅ 创建了 `ThemeToggle.vue` 组件
- ✅ 支持简单切换和下拉菜单两种模式
- ✅ 图标和文字动态显示当前主题状态
- ✅ 平滑的过渡动画效果

### 3. 全局样式适配
- ✅ 配置 Tailwind CSS 暗黑模式支持
- ✅ 添加自定义暗黑模式颜色变量
- ✅ 全局 CSS 变量支持主题切换
- ✅ Arco Design 组件暗黑主题适配

### 4. 布局组件适配
- ✅ Layout.vue 完全适配暗黑模式
- ✅ 侧边栏暗黑模式样式
- ✅ 导航菜单暗黑模式适配
- ✅ 用户菜单暗黑模式适配
- ✅ 主题切换按钮集成到侧边栏

### 5. 页面组件适配
- ✅ Apps 页面暗黑模式适配
- ✅ ApiKeyManage 页面暗黑模式适配
- ✅ Publish 页面暗黑模式适配
- ✅ 表格、按钮、输入框等组件适配

## 使用方法

### 主题切换
1. 在侧边栏底部找到主题切换按钮
2. 点击按钮在浅色/深色模式间切换
3. 或使用下拉菜单选择具体主题模式

### 开发者使用
```typescript
// 在组件中使用主题
import { useThemeStore } from '@/stores/themeStore'

const themeStore = useThemeStore()
const { isDark, mode, toggleDark, setTheme } = themeStore

// 检查是否为暗黑模式
if (isDark) {
  // 暗黑模式逻辑
}

// 切换主题
toggleDark()

// 设置特定主题
setTheme('dark') // 'light' | 'dark' | 'system'
```

### CSS 类名使用
```vue
<template>
  <div :class="[
    'transition-colors duration-300',
    isDark ? 'bg-dark-primary text-dark-primary' : 'bg-white text-gray-900'
  ]">
    内容
  </div>
</template>
```

## 技术特性

### 1. 性能优化
- 使用 CSS 变量减少重复计算
- 平滑的过渡动画（300ms）
- 懒加载主题初始化

### 2. 用户体验
- 记住用户的主题偏好
- 跟随系统主题变化
- 无闪烁的主题切换

### 3. 可访问性
- 适当的颜色对比度
- 语义化的 ARIA 标签
- 键盘导航支持

### 4. 兼容性
- 支持现代浏览器
- 优雅降级处理
- 移动端适配

## 颜色方案

### 浅色模式
- 主背景：#ffffff
- 次背景：#f8fafc
- 文字：#1f2937
- 边框：#e5e7eb

### 暗黑模式
- 主背景：#0f172a
- 次背景：#1e293b
- 文字：#f8fafc
- 边框：#334155

## 测试建议

1. 测试主题切换功能
2. 验证所有页面在暗黑模式下的显示效果
3. 检查表格、表单、模态框等组件的适配
4. 测试系统主题跟随功能
5. 验证主题设置的持久化

## 后续优化

1. 添加更多主题色彩选项
2. 支持自定义主题配置
3. 添加主题切换动画效果
4. 优化移动端体验
