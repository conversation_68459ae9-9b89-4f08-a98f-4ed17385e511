# Shimmer 加载效果使用指南

## 概述

本项目已经集成了完整的 Shimmer 加载效果解决方案，包括：

- `ShimmerLoading` 组件：提供多种类型的骨架屏效果
- `useLoading` composable：管理加载状态
- 全面的暗黑模式支持
- TypeScript 类型支持

## 快速开始

### 1. 基础使用

```vue
<template>
  <!-- 表格骨架屏 -->
  <ShimmerLoading
    v-if="isLoading"
    type="table"
    :rows="5"
    :columns="4"
    :column-widths="['150px', '200px', '100px', '120px']"
  />
  
  <!-- 实际内容 -->
  <a-table v-else :data="tableData" />
</template>

<script setup lang="ts">
import ShimmerLoading from '@/components/ShimmerLoading.vue'
import { useLoading } from '@/composables/useLoading'

const { isLoading, withLoading } = useLoading(['table'])
const isTableLoading = isLoading('table')

// 加载数据
const loadData = async () => {
  await withLoading('table', async () => {
    // 你的异步操作
    await fetchTableData()
  })
}
</script>
```

### 2. 加载状态管理

```typescript
// 初始化多个加载状态
const { isLoading, withLoading, startLoading, stopLoading } = useLoading([
  'users',
  'posts', 
  'comments'
])

// 获取特定的加载状态
const isUsersLoading = isLoading('users')
const isPostsLoading = isLoading('posts')

// 手动控制加载状态
startLoading('users')
stopLoading('users')

// 使用 withLoading 包装异步操作
await withLoading('users', async () => {
  const users = await fetchUsers()
  return users
}, {
  onStart: () => console.log('开始加载'),
  onEnd: () => console.log('加载完成'),
  onError: (error) => console.error('加载失败:', error)
})
```

## Shimmer 组件类型

### 1. 表格骨架屏

```vue
<ShimmerLoading
  type="table"
  :rows="6"
  :columns="5"
  :column-widths="['120px', '150px', '100px', '180px', '100px']"
/>
```

**Props:**
- `rows`: 表格行数 (默认: 5)
- `columns`: 表格列数 (默认: 4)
- `column-widths`: 列宽度数组 (可选)

### 2. 卡片骨架屏

```vue
<ShimmerLoading
  type="card"
  :cards="3"
/>
```

**Props:**
- `cards`: 卡片数量 (默认: 3)

### 3. 列表骨架屏

```vue
<ShimmerLoading
  type="list"
  :items="5"
/>
```

**Props:**
- `items`: 列表项数量 (默认: 6)

### 4. 文本骨架屏

```vue
<ShimmerLoading
  type="text"
  :text-lines="4"
/>
```

**Props:**
- `text-lines`: 文本行数 (默认: 4)

### 5. 自定义骨架屏

```vue
<ShimmerLoading type="custom">
  <div class="flex space-x-4">
    <div class="w-16 h-16 rounded-lg shimmer-light dark:shimmer-dark"></div>
    <div class="flex-1 space-y-3">
      <div class="h-4 rounded-md shimmer-light dark:shimmer-dark"></div>
      <div class="h-4 w-3/4 rounded-md shimmer-light dark:shimmer-dark"></div>
    </div>
  </div>
</ShimmerLoading>
```

## 在现有页面中集成

### Apps.vue 示例

```vue
<template>
  <div>
    <!-- 加载时显示 Shimmer -->
    <ShimmerLoading
      v-if="isAppsLoading"
      type="table"
      :rows="5"
      :columns="5"
      :column-widths="['150px', '120px', '180px', '80px', '120px']"
    />
    
    <!-- 数据表格 -->
    <a-table v-else :data="apps" />
  </div>
</template>

<script setup lang="ts">
import { useLoading } from '@/composables/useLoading'
import ShimmerLoading from '@/components/ShimmerLoading.vue'

const { isLoading, withLoading } = useLoading(['apps'])
const isAppsLoading = isLoading('apps')

const loadApps = async () => {
  await withLoading('apps', async () => {
    await store.reloadApps()
  }, {
    onError: (error) => {
      console.error('加载失败:', error)
      Message.error('加载应用列表失败')
    }
  })
}
</script>
```

## 暗黑模式支持

Shimmer 组件自动适配项目的暗黑模式：

- 浅色模式：使用浅灰色渐变
- 暗黑模式：使用深灰色渐变
- 支持实时主题切换

## 样式自定义

### 自定义动画速度

```css
.shimmer-light,
.shimmer-dark {
  animation-duration: 1.5s; /* 默认是 2s */
}
```

### 自定义颜色

```css
.shimmer-light {
  background: linear-gradient(
    90deg,
    #f8f9fa 25%,
    #e9ecef 50%,
    #f8f9fa 75%
  );
}

.shimmer-dark {
  background: linear-gradient(
    90deg,
    #343a40 25%,
    #495057 50%,
    #343a40 75%
  );
}
```

## 最佳实践

### 1. 保持尺寸一致
确保 shimmer 占用的空间与实际内容相似，避免布局跳跃。

### 2. 合理设置列宽
表格骨架屏应该设置与实际表格相近的列宽。

### 3. 错误处理
使用 `withLoading` 时总是提供错误处理：

```typescript
await withLoading('data', fetchData, {
  onError: (error) => {
    console.error('加载失败:', error)
    Message.error('数据加载失败，请重试')
  }
})
```

### 4. 避免过度使用
只在确实需要的地方使用骨架屏，不要在快速加载的场景中使用。

### 5. 组合使用
可以根据页面内容组合使用不同类型的骨架屏：

```vue
<template>
  <div v-if="isPageLoading">
    <!-- 页面标题 -->
    <ShimmerLoading type="text" :text-lines="1" container-class="mb-6" />
    
    <!-- 卡片列表 -->
    <ShimmerLoading type="card" :cards="3" container-class="mb-8" />
    
    <!-- 数据表格 -->
    <ShimmerLoading type="table" :rows="5" :columns="4" />
  </div>
  
  <div v-else>
    <!-- 实际页面内容 -->
  </div>
</template>
```

## 演示页面

访问 `/shimmer-demo` 路由查看所有类型的 Shimmer 效果演示。

## 故障排除

### 1. Shimmer 不显示
- 检查 `v-if` 条件是否正确
- 确认加载状态是否正确设置

### 2. 样式不生效
- 确认项目中包含了 Tailwind CSS
- 检查暗黑模式类名是否正确应用

### 3. TypeScript 错误
- 确认导入路径正确
- 检查 props 类型是否匹配

## API 参考

### ShimmerLoading Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| type | `'table' \| 'card' \| 'list' \| 'text' \| 'custom'` | `'custom'` | 骨架屏类型 |
| rows | `number` | `5` | 表格行数 |
| columns | `number` | `4` | 表格列数 |
| cards | `number` | `3` | 卡片数量 |
| items | `number` | `6` | 列表项数量 |
| textLines | `number` | `4` | 文本行数 |
| columnWidths | `string[]` | `[]` | 列宽度数组 |
| containerClass | `string` | `''` | 容器额外样式类 |

### useLoading 返回值

| 方法/属性 | 类型 | 说明 |
|-----------|------|------|
| isLoading | `(key: string) => ComputedRef<boolean>` | 获取加载状态 |
| startLoading | `(key: string) => void` | 开始加载 |
| stopLoading | `(key: string) => void` | 停止加载 |
| withLoading | `<T>(key: string, fn: () => Promise<T>, options?) => Promise<T>` | 包装异步操作 |
| setLoading | `(key: string, value: boolean) => void` | 设置加载状态 |
| hasAnyLoading | `ComputedRef<boolean>` | 是否有任何加载状态 | 