module.exports = {
  root: true,
  env: {
    es2020: true,
    browser: true,
    node: true
  },
  extends: [
    'plugin:vue/vue3-essential',
    'eslint:recommended',
    '@vue/typescript/recommended',
    '@vue/prettier',
    '@vue/prettier/@typescript-eslint'
  ],
  parserOptions: {
    ecmaVersion: 2020,
    parser: '@typescript-eslint/parser'
  },
  globals: {
    __static: true,
    __windowUrls: true,
    __preloads: true,
    __workers: true,
    NodeJS: true
  },
  rules: {
    'no-console': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    'no-debugger': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    'space-before-function-paren': 0,
    'vue/no-multiple-template-root': 0,
    'import/no-absolute-path': 0,
    '@typescript-eslint/no-explicit-any': 0,
    '@typescript-eslint/no-empty-function': 'off',
    'prettier/prettier': [
      'error',
      {
        singleQuote: true,
        semi: false,
        jsxSingleQuote: true,
        printWidth: 85,
        tabWidth: 2,
        trailingComma: 'none',
        'lines-between-class-members': 'always'
      }
    ],
    'vue/multi-word-component-names': 'off',
    'vue/no-useless-template-attributes': 'warn',
    '@typescript-eslint/no-inferrable-types': 'warn',
    'prefer-const': 'warn'
  },
  ignorePatterns: ['node_modules/**', 'dist/**', 'libs/**']
}
