{"extends": "@vue/tsconfig/tsconfig.web.json", "include": ["env.d.ts", "src/**/*", "src/**/*.vue", "rig_dev/**/*.ts", "rig_dev/**/*.vue", "vite.config.*", "vitest.config.*", "cypress.config.*", "playwright.config.*", "shims-vue.d.ts"], "exclude": ["node_modules"], "compilerOptions": {"baseUrl": ".", "paths": {"@/*": ["./src/*"], "tslib": ["node_modules/tslib/tslib.d.ts"]}, "lib": ["es2020", "DOM", "DOM.Iterable", "scripthost", "WebWorker"], "target": "ES2020", "module": "es2020", "strict": true, "jsx": "preserve", "importHelpers": true, "allowJs": true, "useUnknownInCatchVariables": false, "moduleResolution": "node", "esModuleInterop": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "sourceMap": true, "resolveJsonModule": true, "composite": true, "types": ["node", "jest"], "verbatimModuleSyntax": true}}